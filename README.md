# 战AI

#### 介绍
HP的AI助手

#### 软件架构
基于springboot, 接入智普AI

#### 客户端接口

1.  设备登录, 设备上报SN/PN, 并通过惠管家授权服务获取是否存在授权. 
2.  根据设备授权, 登录返回中携带, isCloud, 标识是否可以继续使用云端服务.
3.  AI接口
4.  智普交互接口

#### 管理端接口

1. 登录
2. 设备管理 - 设备列表, 设备授权, 设备授权列表, 支持对单独设备进行手动授权, 授权信息应保存在授权中心.
3. 智普AI接口管理
4. 各类统计数据
5. 

#### 疑问点

1. 如果是我们的客户端中调用了一个cmd命令行工具, 但是这个不能显示给用户看到,且要保持运行, 是否可行?
2. 客户端安装包中是否可以加入sqllite数据库, 用于存储一些数据, 但是不需要用户关心, 且不需要用户操作.
3. 文本等内容的解析, 保存
4. 是否可以直接在我们的客户端中, 增加anythingllm, 或者其他的RAG客户端, 参考cherry studio

#### 本地知识库流程

1. 用户上传文件至桌面程序, 桌面程序解析文件, 提供给向量模型进行向量化, 并保存向量数据
