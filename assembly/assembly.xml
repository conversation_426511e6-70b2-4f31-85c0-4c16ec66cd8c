<assembly>
    <!-- 打包文件名的标识符，用来做后缀-->
    <id>assembly</id>
    <!-- formats是assembly插件支持的打包文件格式，有zip、tar、tar.gz、tar.bz2、jar、war。可以同时定义多个format。-->
    <formats>
        <format>tar.gz</format>
        <format>zip</format>
    </formats>

    <includeBaseDirectory>true</includeBaseDirectory>

    <!--文件设置-->
    <fileSets>
        <!--
            0755->即用户具有读/写/执行权限，组用户和其它用户具有读写权限；
            0644->即用户具有读写权限，组用户和其它用户具有只读权限；
        -->
        <fileSet>
            <directory>target</directory>
            <outputDirectory>${project.artifactId}</outputDirectory>
            <includes>
                <include>*.jar</include>
            </includes>
        </fileSet>
        <!-- 将项目说明文档打包到docs目录中 -->
        <fileSet>
            <directory>.</directory>
            <outputDirectory>${project.artifactId}</outputDirectory>
            <includes>
                <include>Dockerfile</include>
            </includes>
            <fileMode>0644</fileMode>
        </fileSet>
        <fileSet>
            <directory>.</directory>
            <outputDirectory>.</outputDirectory>
            <includes>
                <include>*.yaml</include>
            </includes>
            <fileMode>0755</fileMode>
        </fileSet>
    </fileSets>

</assembly>
