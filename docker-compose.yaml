version: "3.8"
services:
  spider:
    build:
      context: spider
    container_name: spider
    environment:
      - TZ=Asia/Shanghai
      - MYSQL_HOST=
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=
      - MYSQL_USERNAME=
      - MYSQL_PASSWORD=
      - REDIS_HOST=
      - REDIS_PORT=6379
      - REDIS_DATABASE=0
      - REDIS_PASSWORD=
    image: spider
    ports:
      - 8070:8070
    restart: always
    volumes:
      - ./log:/home/<USER>
