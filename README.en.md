# 战AI_三方AI接口

#### Description

在战AI中, 提供接口供第三方调用ai-pc的本地AI能力

#### Software Architecture

所有接口参考openai标准格式

#### Installation

## 1. 启动服务

**接口地址**:`{{baseUrl}/{baseVersion}/server/{status}`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


status = startup / shutdown


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- |--------| 
|200|OK| code   |


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|返回标记：成功标记=0，失败标记=1|integer(int32)|integer(int32)|
|msg|返回信息|string||
|result||string||


**响应示例**:
```json
{
	"code": 0,
	"msg": "服务启动失败 / 本机器不支持本地AI",
	"result": "success/failed"
}
```

## 2. 获取可用模型列表


**接口地址**:`{{baseUrl}/{baseVersion}/models`


**请求方式**:`GET`


**响应数据类型**:`*/*`


**请求参数**:


暂无


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- |--------| 
|200|OK| code   |


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|返回标记：成功标记=0，失败标记=1|integer(int32)|integer(int32)|
|msg|返回信息|string||
|result||string||


**响应示例**:
```json
{
	"code": 0,
	"msg": "",
    "data": [
        {
            "id": "model-id",
            "object": "model",
            "created": 1234567890,
            "owned_by": "user-id",
            "permission": [
                {
                    "id": "permission-id",
                    "object": "permission",
                    "created": 1234567890,
                    "allow_create_engine": true,
                    "allow_sampling": true,
                    "allow_logprobs": false,
                    "allow_search_indices": false,
                    "allow_view": true,
                    "allow_fine_tuning": false,
                    "organization": "*",
                    "group": null,
                    "is_blocking": false
                }
            ]
        }
    ],
	"result": "success/failed"
}
```

## 3. 模型对话-单轮对话

**接口地址**:`{{baseUrl}/{baseVersion}/chat/completions`


**请求方式**:`POST`


**响应数据类型**:`*/*`


**请求参数**:

```json
{
  "model": "xxxx",
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user",
      "content": "Hello, who won the world series in 2020?"
    }
  ],
    "temperature": 0.7,
    "top_p": 1.0,
    "max_tokens": 150,
    "n": 1,
    "stream": false
}
```


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- |--------| 
|200|OK| code   |


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|返回标记：成功标记=0，失败标记=1|integer(int32)|integer(int32)|
|msg|返回信息|string||
|result||string||


**响应示例**:

流式回复
```json
{
  "id": "chatcmpl-711",
  "object": "chat.completion.chunk",
  "created": 1750744220,
  "model": "qwen3:8b",
  "system_fingerprint": "fp_ollama",
  "choices": [
    {
      "index": 0,
      "delta": {
        "role": "assistant",
        "content": "<think>"
      },
      "finish_reason": null
    }
  ]
}
```
阻塞回复
```json
{
  "id": "chatcmpl-544",
  "object": "chat.completion",
  "created": 1750744297,
  "model": "qwen3:8b",
  "system_fingerprint": "fp_ollama",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "<think>\n好的，用户问\"你是谁\"，我需要回答。作为惠普的AI助手，我应该明确自己的身份和功能。首先，要说明我是惠普的AI助手，专注于提供技术支持和产品信息。然后，要提到我的功能范围，比如帮助用户了解产品、解答技术问题、提供使用建议等。同时，我需要保持友好和专业的语气，让用户感到亲切。另外，还要注意不要使用过于复杂的术语，保持回答简洁明了。可能用户对惠普的产品不太熟悉，所以需要适当引导他们提问，以便我能更好地提供帮助。最后，确保回答符合品牌形象，传递积极和可靠的信息。现在组织这些点，形成自然流畅的回应。\n</think>\n\n您好！我是惠普的AI智能助手，专注于为您提供技术支持和产品相关信息。无论您是想了解惠普产品的功能、使用方法，还是遇到技术问题需要帮助，我都可以为您提供详细的解答和指导。有什么我可以帮您的吗？ 😊"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 23,
    "completion_tokens": 203,
    "total_tokens": 226
  }
}
```


