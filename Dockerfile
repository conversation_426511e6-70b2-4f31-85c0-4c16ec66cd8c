FROM registry.cn-shanghai.aliyuncs.com/genius-docker-space/java:17-jre

MAINTAINER <EMAIL>

ENV TZ=Asia/Shanghai

ENV JAVA_OPTS="-Xms512m -Xmx1024m -Djava.security.egd=file/dev/./urandom"

RUN ln -sf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

RUN mkdir -p /opt/projects/zhan-ai

WORKDIR /opt/projects/zhan-ai

# 替换为实际端口
EXPOSE 8070

COPY ./target/zhan-ai.jar ./

CMD java -jar -Xms512m -Xmx512m -Xss256k -Dfile.encoding=UTF-8 zhan-ai.jar
