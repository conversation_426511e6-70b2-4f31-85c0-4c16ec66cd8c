version: '1.0'
name: branch-pipeline
displayName: BranchPipeline
triggers:
  trigger: manual
  pr:
    branches:
      prefix:
        - ''
stages:
  - name: compile
    displayName: 编译
    strategy: naturally
    trigger: auto
    steps:
      - step: build@maven
        name: build_maven
        displayName: Maven 构建
        jdkVersion: '17'
        mavenVersion: 3.9.6
        commands:
          - mvn -B clean package -Dmaven.test.skip=true
        artifacts:
          - name: BUILD_ARTIFACT
            path:
              - ./target
        settings: []
        caches:
          - ~/.m2
        strategy: {}
      - step: build@docker
        name: build_docker
        displayName: 镜像构建
        type: cert
        certificate: 0c125590-2e47-013e-0867-6ad96e0d97ee
        tag: ${GITEE_REPO}:0.${GITEE_PIPELINE_BUILD_NUMBER}
        dockerfile: ./Dockerfile
        context: ''
        artifacts:
          - ${BUILD_ARTIFACT}
        isCache: false
        parameter: {}
        notify: []
        strategy:
          retry: '0'
        dependsOn: build_maven
