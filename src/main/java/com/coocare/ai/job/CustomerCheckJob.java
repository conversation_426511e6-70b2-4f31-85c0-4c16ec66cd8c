package com.coocare.ai.job;

import com.coocare.ai.service.AiCustomerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

@Slf4j
@Configuration
@EnableScheduling
@RequiredArgsConstructor
public class CustomerCheckJob {

    private final AiCustomerService customerService;
    private final StringRedisTemplate redisTemplate;

    // 每天凌晨0点执行 用户如果超过31天后, 清空赠送的token
    @Scheduled(cron = "0 0 0 * * ? ")
    private void customerCheck() {
        customerService.customerCheck();
    }

    //每日0点, 记录用户签到信息
    @Scheduled(cron = "0 0 0 * * ? ")
    private void customerSign() {
        log.info("用户签到信息处理");
        customerService.customerSign();
    }

//    @PostConstruct
//    private void init() {
//        List<AiCustomer> customerList = customerService.list();
//        customerList.forEach((customer) -> {
//            if (customer.getCreateTime() != null && ChronoUnit.DAYS.between(customer.getCreateTime().toLocalDate(), LocalDateTime.now().toLocalDate()) < 31) {
//                String signList = redisTemplate.opsForValue().get("sign:" + customer.getCustomerId());
//                int days = (int) ChronoUnit.DAYS.between(customer.getCreateTime().toLocalDate(), LocalDateTime.now().toLocalDate());
//                log.info("用户{}的注册时间{}. 签到信息: {}, 签到长度: {}", customer.getDeviceSn(), days, signList, signList.length());
//                if (days < signList.length()) {
//                    StringBuilder sign = new StringBuilder(signList);
//                    for (int i = 0; i < days - signList.length(); i++) {
//                        sign.append("0");
//                    }
//                    redisTemplate.opsForValue().set("sign:" + customer.getCustomerId(), sign.toString());
//                }
//            }
//        });
//    }

}
