package com.coocare.ai.entity.sys.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.coocare.ai.entity.sys.SysRole;
import com.coocare.ai.entity.sys.SysUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2017/11/11
 */
@Data
@Schema(description = "用户信息")
public class UserInfo implements Serializable {

	/**
	 * 用户基本信息
	 */
//	@Schema(description = "用户基本信息")
//	private SysUser sysUser;

	@Schema(description = "用户ID")
	private Long userId;

	@Schema(description = "用户名")
	private String username;

	@Schema(description = "密码")
	private String password;

	@Schema(description = "盐值")
	private String salt;

	@Schema(description = "电话号码")
	private String phone;

	@Schema(description = "头像")
	private String avatar;

	@Schema(description = "昵称")
	private String nickname;

	@Schema(description = "姓名")
	private String name;

	/**
	 * 权限标识集合
	 */
	@Schema(description = "权限标识集合")
	private Set<String> permissions;

	/**
	 * 角色集合
	 */
	@Schema(description = "角色标识集合")
	private List<SysRole> roleList;

}
