package com.coocare.ai.entity.api;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/14
 */

@Schema
@Data
@TableName(value = "users")
public class ApiUsers implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description="")
    private Long id;

    @TableField(value = "username")
    @Schema(description="")
    private String username;

    @TableField(value = "`password`")
    @Schema(description="")
    private String password;

    @TableField(value = "display_name")
    @Schema(description="")
    private String displayName;

    @TableField(value = "`role`")
    @Schema(description="")
    private Long role;

    @TableField(value = "`status`")
    @Schema(description="")
    private Long status;

    @TableField(value = "email")
    @Schema(description="")
    private String email;

    @TableField(value = "github_id")
    @Schema(description="")
    private String githubId;

    @TableField(value = "wechat_id")
    @Schema(description="")
    private String wechatId;

    @TableField(value = "lark_id")
    @Schema(description="")
    private String larkId;

    @TableField(value = "oidc_id")
    @Schema(description="")
    private String oidcId;

    @TableField(value = "access_token")
    @Schema(description="")
    private String accessToken;

    @TableField(value = "quota")
    @Schema(description="")
    private Long quota;

    @TableField(value = "used_quota")
    @Schema(description="")
    private Long usedQuota;

    @TableField(value = "request_count")
    @Schema(description="")
    private Long requestCount;

    @TableField(value = "`group`")
    @Schema(description="")
    private String group;

    @TableField(value = "aff_code")
    @Schema(description="")
    private String affCode;

    @TableField(value = "inviter_id")
    @Schema(description="")
    private Long inviterId;

    @TableField(value = "telegram_id")
    @Schema(description="")
    private String telegramId;

    @TableField(value = "aff_count")
    @Schema(description="")
    private Long affCount;

    @TableField(value = "aff_quota")
    @Schema(description="")
    private Long affQuota;

    @TableField(value = "aff_history")
    @Schema(description="")
    private Long affHistory;

    @TableField(value = "deleted_at")
    @Schema(description="")
    private LocalDateTime deletedAt;

    @TableField(value = "linux_do_id")
    @Schema(description="")
    private String linuxDoId;

    @TableField(value = "setting")
    @Schema(description="")
    private String setting;

    @TableField(value = "remark")
    @Schema(description="")
    private String remark;
}
