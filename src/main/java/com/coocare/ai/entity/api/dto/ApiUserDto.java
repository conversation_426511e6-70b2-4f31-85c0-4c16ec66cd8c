package com.coocare.ai.entity.api.dto;


import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description: AI注册用户返回实例
 * @author: Adam
 * @create: 2025-04-25 15:25
 **/

@Data
public class ApiUserDto {

    @Schema(description = "")
    private Long id;
    @TableField(value = "username")
    @Schema(description = "")
    private String username;
    @Schema(description = "")
    private Long status;
    @Schema(description = "")
    private Long quota;
    @TableField(value = "used_quota")
    @Schema(description = "")
    private Long usedQuota;
    @TableField(value = "request_count")
    @Schema(description = "")
    private Long requestCount;
    @TableField(value = "`group`")
    @Schema(description = "")
    private String group;
}
