package com.coocare.ai.entity.api;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/14
 */

@Schema
@Data
@TableName(value = "tokens")
public class ApiTokens implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @TableField(value = "user_id")
    @Schema(description = "")
    private Long userId;
    @TableField(value = "`key`")
    @Schema(description = "")
    private String key;
    @TableField(value = "`status`")
    @Schema(description = "")
    private Long status;
    @TableField(value = "`name`")
    @Schema(description = "")
    private String name;
    @TableField(value = "created_time")
    @Schema(description = "")
    private Long createdTime;
    @TableField(value = "accessed_time")
    @Schema(description = "")
    private Long accessedTime;
    @TableField(value = "expired_time")
    @Schema(description = "")
    private Long expiredTime;
    @TableField(value = "remain_quota")
    @Schema(description = "")
    private Long remainQuota;
    @TableField(value = "unlimited_quota")
    @Schema(description = "")
    private Boolean unlimitedQuota;
    @TableField(value = "used_quota")
    @Schema(description = "")
    private Long usedQuota;
    @TableField(value = "models")
    @Schema(description = "")
    private String models;
    @TableField(value = "subnet")
    @Schema(description = "")
    private String subnet;
    @TableField(value = "model_limits_enabled")
    @Schema(description = "")
    private Boolean modelLimitsEnabled;
    @TableField(value = "model_limits")
    @Schema(description = "")
    private String modelLimits;
    @TableField(value = "allow_ips")
    @Schema(description = "")
    private String allowIps;
    @TableField(value = "`group`")
    @Schema(description = "")
    private String group;
    @TableField(value = "deleted_at")
    @Schema(description = "")
    private Date deletedAt;
}
