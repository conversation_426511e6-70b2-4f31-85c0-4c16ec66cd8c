package com.coocare.ai.entity.api;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Schema
@Data
@TableName(value = "`logs`")
public class ApiLogs implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description="")
    private Long id;

    @TableField(value = "user_id")
    @Schema(description="")
    private Long userId;

    @TableField(value = "created_at")
    @Schema(description="")
    private Long createdAt;

    @TableField(value = "`type`")
    @Schema(description="")
    private Long type;

    @TableField(value = "content")
    @Schema(description="")
    private String content;

    @TableField(value = "username")
    @Schema(description="")
    private String username;

    @TableField(value = "token_name")
    @Schema(description="")
    private String tokenName;

    @TableField(value = "model_name")
    @Schema(description="")
    private String modelName;

    @TableField(value = "quota")
    @Schema(description="")
    private Long quota;

    @TableField(value = "prompt_tokens")
    @Schema(description="")
    private Long promptTokens;

    @TableField(value = "completion_tokens")
    @Schema(description="")
    private Long completionTokens;

    @TableField(value = "channel_id")
    @Schema(description="")
    private Long channelId;

    @TableField(value = "use_time")
    @Schema(description="")
    private Long useTime;

    @TableField(value = "is_stream")
    @Schema(description="")
    private Boolean isStream;

    @TableField(value = "channel_name")
    @Schema(description="")
    private String channelName;

    @TableField(value = "token_id")
    @Schema(description="")
    private Long tokenId;

    @TableField(value = "`group`")
    @Schema(description="")
    private String group;

    @TableField(value = "other")
    @Schema(description="")
    private String other;

    @TableField(value = "ip")
    @Schema(description="")
    private String ip;

    @TableField(exist = false)
    private LocalDateTime createdTime;

    @TableField(exist = false)
    private Integer allQuota;

}