package com.coocare.ai.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 支付回调记录表
 */
@Schema(description = "支付回调记录表")
@Data
@TableName(value = "payment_notify")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PaymentNotify implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    @TableField(value = "channel")
    @Schema(description = "支付渠道")
    private String channel;

    @TableField(value = "device_sn")
    @Schema(description = "设备SN")
    private String deviceSn;

    @TableField(value = "price")
    @Schema(description = "支付金额(分)")
    private Long price;

    @TableField(value = "timestamp")
    @Schema(description = "时间戳")
    private Long timestamp;

    @TableField(value = "sku")
    @Schema(description = "SKU")
    private String sku;

    @TableField(value = "status")
    @Schema(description = "处理状态 0-待处理 1-处理成功 2-处理失败")
    private Integer status;

    @TableField(value = "error_msg")
    @Schema(description = "错误信息")
    private String errorMsg;

    @TableField(value = "raw_data")
    @Schema(description = "原始回调数据")
    private String rawData;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}