package com.coocare.ai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 应用信息
 */
@Schema(description = "应用信息")
@Data
@TableName(value = "app_info")
public class AppInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "app_id", type = IdType.AUTO)
    @Schema(description = "")
    private Long appId;

    /**
     * 应用的名称
     */
    @TableField(value = "`name`")
    @Schema(description = "应用的名称")
    private String name;

    /**
     * 应用类型
     */
    @TableField(value = "`type`")
    @Schema(description = "应用类型")
    private String type;

    /**
     * 应用的图片
     */
    @TableField(value = "icon")
    @Schema(description = "应用的图片")
    private String icon;

    /**
     * 应用简介
     */
    @TableField(value = "info")
    @Schema(description = "应用简介")
    private String info;

    /**
     * 应用标签
     */
    @TableField(value = "tags")
    @Schema(description = "应用标签")
    private String tags;

    /**
     * 应用的介绍
     */
    @TableField(value = "detail")
    @Schema(description = "应用的介绍")
    private String detail;

    /**
     * 下载链接
     */
    @TableField(value = "intel_download_url")
    @Schema(description = "下载链接")
    private String intelDownloadUrl;

    /**
     * 核心要求
     */
    @TableField(value = "intel_core_requirement")
    @Schema(description = "核心要求")
    private Integer intelCoreRequirement;

    /**
     * 内存要求
     */
    @TableField(value = "intel_memery_requirement")
    @Schema(description = "内存要求")
    private Integer intelMemeryRequirement;

    /**
     * 下载链接
     */
    @TableField(value = "amd_download_url")
    @Schema(description = "下载链接")
    private String amdDownloadUrl;

    /**
     * 核心要求
     */
    @TableField(value = "amd_core_requirement")
    @Schema(description = "核心要求")
    private String amdCoreRequirement;

    /**
     * 内存要求
     */
    @TableField(value = "amd_memery_requirement")
    @Schema(description = "内存要求")
    private String amdMemeryRequirement;

    /**
     * 版本号
     */
    @TableField(value = "version")
    @Schema(description = "版本号")
    private String version;

    /**
     * 排序
     */
    @TableField(value = "sort")
    @Schema(description = "排序")
    private Integer sort;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 跳转地址
     */
    @TableField(value = "link")
    @Schema(description = "跳转地址")
    private String link;

    @TableField(exist = false)
    private String categoryName;

    @TableField(exist = false)
    private Long categoryId;
}