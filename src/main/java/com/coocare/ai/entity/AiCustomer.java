package com.coocare.ai.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 客户端注册表
 * <AUTHOR>
 * @date 2025/2/14
 */

@Schema(description = "客户端注册表")
@Data
@TableName(value = "ai_customer")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AiCustomer implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "customer_id", type = IdType.ASSIGN_ID)
    @Schema(description = "")
    private Long customerId;

    /**
     * 用户名
     */
    @TableField(value = "`name`")
    @Schema(description = "用户名")
    private String name;

    /**
     * 设备SN
     */
    @TableField(value = "device_sn")
    @Schema(description = "设备SN")
    private String deviceSn;

    /**
     * 设备PN
     */
    @TableField(value = "device_pn")
    @Schema(description = "设备PN")
    private String devicePn;

    /**
     * 系统token
     */
    @TableField(value = "sys_token")
    @Schema(description = "系统token")
    private String sysToken;

    /**
     * AI聊天token
     */
    @TableField(value = "ai_token")
    @Schema(description = "AI聊天token")
    private String aiToken;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT)
    private LocalDateTime updateTime;

    /**
     * 过期时间
     */
    @TableField(value = "expired_time")
    @Schema(description = "过期时间")
    private LocalDateTime expiredTime;

    @TableField(value = "del_flag")
    @Schema(description = "")
    private String delFlag;
}
