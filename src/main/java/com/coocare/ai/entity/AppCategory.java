package com.coocare.ai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

/**
 * 应用的分类
 */
@Schema(description="应用的分类")
@Data
@TableName(value = "app_category")
public class AppCategory implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 分类的ID
     */
    @TableId(value = "category_id", type = IdType.ASSIGN_ID)
    @Schema(description="分类的ID")
    private Long categoryId;

    /**
     * 分类名称
     */
    @TableField(value = "category_name")
    @Schema(description="分类名称")
    private String categoryName;

    /**
     * 分类的排序
     */
    @TableField(value = "sort")
    @Schema(description="分类的排序")
    private Integer sort;
}