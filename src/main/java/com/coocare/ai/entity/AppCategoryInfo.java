package com.coocare.ai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

/**
 * 应用分类关联表
 */
@Schema(description="应用分类关联表")
@Data
@TableName(value = "app_category_info")
public class AppCategoryInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 应用ID
     */
    @TableField(value = "info_id")
    @Schema(description="应用ID")
    private Long infoId;

    /**
     * 分类ID
     */
    @TableField(value = "category_id")
    @Schema(description="分类ID")
    private Long categoryId;
}