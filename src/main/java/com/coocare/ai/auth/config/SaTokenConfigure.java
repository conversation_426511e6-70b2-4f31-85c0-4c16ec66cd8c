package com.coocare.ai.auth.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import com.coocare.ai.auth.support.StpKit;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


/**
 * [Sa-Token 权限认证] 配置类
 * <AUTHOR>
 *
 */
@Configuration
public class SaTokenConfigure implements WebMvcConfigurer {

	/**
	 * 注册 Sa-Token 拦截器打开注解鉴权功能
	 */
	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		// 注册 Sa-Token 拦截器，校验规则为 StpUtil.checkLogin() 登录校验。
		registry.addInterceptor(new SaInterceptor(handle -> {
					SaRouter.match("/sys/**").check(r -> StpUtil.checkLogin());
					SaRouter.match("/client/**").check(r -> StpKit.CUSTOMER.checkLogin());
				}))
				.addPathPatterns("/**")
				.excludePathPatterns("/login", "/rest/login", "/client/check", "/client/active",
						"/swagger-resources/**",
						"/webjars/**",
						"/v2/**",
						"/swagger-ui.html/**",
						"/api-docs",
						"/api-docs/**",
						"/doc.html/**");
	}
}
