package com.coocare.ai.utils;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.coocare.ai.config.exception.RRException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class AiUtils {

    @Value("${ai.server.url}")
    private String serverUrl;

    public void register(String body) {
        String response = HttpRequest.post(serverUrl + "/api/user/register")
                .body(body)
                .execute().body();
        JSONObject jsonObject = JSON.parseObject(response);
        if (!jsonObject.getBoolean("success")) {
            throw new RRException(jsonObject.getString("message"));
        }
    }

    public void updateUser(String body) {
        HttpResponse response = HttpRequest.put(serverUrl + "/api/user").header("new-api-user", "1").header("Authorization", "Bearer UvqoFiwxmgemxtL4dzm081hiNCbFwg==")
                .body(body)
                .execute();
        JSONObject jsonObject = JSON.parseObject(response.body());
        if (!jsonObject.getBoolean("success")) {
            throw new RRException(jsonObject.getString("message"));
        }
    }

    /**
     * 输入兑换码兑换
     *
     * @param key
     */
    public String topup(String key, String userId, String token) {
        HttpResponse response = HttpRequest.post(serverUrl + "/api/user/topup").header("new-api-user", userId).header("Authorization", "Bearer " + token)
                .body(JSON.toJSONString(Map.of("key", key)))
                .execute();
        JSONObject jsonObject = JSON.parseObject(response.body());
        if (!jsonObject.getBoolean("success")) {
            throw new RRException(jsonObject.getString("message"));
        }
        return jsonObject.getString("data");
    }

}
