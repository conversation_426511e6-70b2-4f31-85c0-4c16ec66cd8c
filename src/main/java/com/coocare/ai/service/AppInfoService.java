package com.coocare.ai.service;

import com.coocare.ai.entity.AppInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;

import java.util.List;
import java.util.Map;

public interface AppInfoService extends IService<AppInfo>{

    PageUtils getAppListByCategoryId(Long categoryId, PageDomain pageDomain);

    PageUtils getAppList(String name, String type, PageDomain pageDomain);

    Map<String, List<AppInfo>> getAppIndex();

    void assignCategory(List<Long> appIds, Long categoryId);
}
