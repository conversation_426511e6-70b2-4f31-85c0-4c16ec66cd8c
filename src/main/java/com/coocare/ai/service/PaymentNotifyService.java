package com.coocare.ai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocare.ai.entity.PaymentNotify;

public interface PaymentNotifyService extends IService<PaymentNotify> {
    
    /**
     * 保存支付回调记录
     */
    PaymentNotify saveNotifyRecord(String channel, String deviceSn, Long price, Long timestamp, String sku);
    
    /**
     * 更新处理状态
     */
    void updateStatus(Long id, Integer status, String errorMsg);
}