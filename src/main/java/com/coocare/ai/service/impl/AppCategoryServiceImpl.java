package com.coocare.ai.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocare.ai.mapper.AppCategoryMapper;
import com.coocare.ai.entity.AppCategory;
import com.coocare.ai.service.AppCategoryService;
@Service
public class AppCategoryServiceImpl extends ServiceImpl<AppCategoryMapper, AppCategory> implements AppCategoryService{

    @Override
    public List<AppCategory> getCategoryList() {
        return list(Wrappers.lambdaQuery(AppCategory.class).orderByAsc(AppCategory::getSort));
    }
}
