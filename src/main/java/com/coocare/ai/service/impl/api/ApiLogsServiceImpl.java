package com.coocare.ai.service.impl.api;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocare.ai.entity.api.ApiLogs;
import com.coocare.ai.mapper.api.ApiLogsMapper;
import com.coocare.ai.service.api.ApiLogsService;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.ZoneId;
import java.util.stream.Collectors;

@DS("slave")
@Service
public class ApiLogsServiceImpl extends ServiceImpl<ApiLogsMapper, ApiLogs> implements ApiLogsService {

    @Override
    public PageUtils queryPage(Long userId, PageDomain pageDomain) {
        IPage<ApiLogs> page = this.page(new Page<>(pageDomain.getPageNo(), pageDomain.getPageSize()),
                Wrappers.lambdaQuery(ApiLogs.class).eq(ApiLogs::getUserId, userId).orderByDesc(ApiLogs::getCreatedAt));
        page.setRecords(page.getRecords().stream().peek(log -> log.setCreatedTime(Instant.ofEpochSecond(log.getCreatedAt()).atZone(ZoneId.systemDefault())
                .toLocalDateTime())).collect(Collectors.toList()));
        return new PageUtils(page);
    }
}
