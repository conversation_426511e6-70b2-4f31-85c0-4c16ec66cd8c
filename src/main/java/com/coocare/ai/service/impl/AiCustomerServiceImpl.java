package com.coocare.ai.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocare.ai.auth.support.StpKit;
import com.coocare.ai.config.exception.RRException;
import com.coocare.ai.entity.AiCustomer;
import com.coocare.ai.entity.api.ApiTokens;
import com.coocare.ai.entity.api.ApiUsers;
import com.coocare.ai.mapper.AiCustomerMapper;
import com.coocare.ai.service.AiCustomerService;
import com.coocare.ai.service.api.ApiTokensService;
import com.coocare.ai.service.api.ApiUsersService;
import com.coocare.ai.utils.AiUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/2/14
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class AiCustomerServiceImpl extends ServiceImpl<AiCustomerMapper, AiCustomer> implements AiCustomerService {

    private final AiUtils aiUtils;
    private final ApiTokensService apiTokensService;
    private final RedisTemplate<String, String> redisTemplate;
    private final ApiUsersService apiUsersService;

    @Override
    public Map<String, Object> deviceActive(JSONObject data) {
        String sn = data.getString("sn");
        String pn = data.getString("pn");
        AiCustomer customer = getOne(Wrappers.<AiCustomer>lambdaQuery().eq(AiCustomer::getDeviceSn, sn).eq(AiCustomer::getDevicePn, pn));
        if (Optional.ofNullable(customer).isEmpty()) {
            //校验是否为已注册惠管家设备
            customer = new AiCustomer();
            customer.setDeviceSn(sn);
            customer.setDevicePn(pn);
            aiUtils.register(JSON.toJSONString(Map.of("username", sn, "password", pn, "password2", pn)));
            String token = apiTokensService.getOne(Wrappers.<ApiTokens>lambdaQuery().eq(ApiTokens::getName, sn + "的初始令牌")).getKey();
            apiUsersService.update(Wrappers.<ApiUsers>lambdaUpdate().set(ApiUsers::getAccessToken, sn).set(ApiUsers::getSetting, "{\"record_ip_log\":true}").eq(ApiUsers::getUsername, sn));
            customer.setAiToken(token);
            save(customer);
        }
        StpKit.CUSTOMER.login(customer.getCustomerId());
        if (!redisTemplate.hasKey("sign:" + StpKit.CUSTOMER.getLoginIdAsString())) {
            redisTemplate.opsForValue().set("sign:" + StpKit.CUSTOMER.getLoginIdAsString(), "1");
        }
        return Map.of("token", StpKit.CUSTOMER.getTokenValue(),
                "sn", data.get("sn"),
                "pn", data.get("pn"),
                "openId", customer.getCustomerId(),
                "aiToken", customer.getAiToken());
    }

    @Override
    public Map<String, Object> deviceLogin(JSONObject data) {
        AiCustomer customer = getOne(Wrappers.<AiCustomer>lambdaQuery().eq(AiCustomer::getDeviceSn, data.get("sn")).eq(AiCustomer::getDevicePn, data.get("pn")));
        Optional.ofNullable(customer).orElseThrow(() -> new RRException("device.not.exist"));
        if (customer.getExpiredTime().isBefore(LocalDateTime.now())) {
            throw new RRException("device.expired");
        }
        StpKit.CUSTOMER.login(customer.getCustomerId());
        return Map.of("token", StpKit.CUSTOMER.getTokenValue(),
                "sn", data.get("sn"),
                "pn", data.get("pn"),
                "openId", customer.getCustomerId(),
                "aiToken", customer.getAiToken());
    }

    @Override
    public void topup() {
        AiCustomer customer = getById(StpKit.CUSTOMER.getLoginIdAsLong());
        if (customer.getCreateTime().toLocalDate().isAfter(customer.getCreateTime().plusDays(31L).toLocalDate())) {
            throw new  RRException("device.expired");
        } else {

        }
    }

    @Override
    public void customerCheck() {
        List<AiCustomer> customerList = list(Wrappers.lambdaQuery(AiCustomer.class).isNull(AiCustomer::getExpiredTime));
        customerList.forEach(customer -> {
            if (customer.getCreateTime().toLocalDate().isAfter(customer.getCreateTime().plusDays(31L).toLocalDate())) {
                customer.setExpiredTime(LocalDateTime.now());
                updateById(customer);
                if (redisTemplate.hasKey("sign:" + customer.getCustomerId())) {
                    redisTemplate.delete("sign:" + customer.getCustomerId());
                }
            }
        });
    }

    @Override
    public void customerSign() {
        LocalDateTime startTime = LocalDate.now().minusDays(31).atStartOfDay();
        List<AiCustomer> customerList = list(Wrappers.lambdaQuery(AiCustomer.class).ge(AiCustomer::getCreateTime, startTime));
        customerList.forEach(customer -> {
            if (redisTemplate.hasKey("sign:" + customer.getCustomerId())) {
                String sign = redisTemplate.opsForValue().get("sign:" + customer.getCustomerId());
                log.info("sign:{}", sign);
                redisTemplate.opsForValue().set("sign:" + customer.getCustomerId(), sign + "0");
            } else {
                redisTemplate.opsForValue().set("sign:" + customer.getCustomerId(), "1");
            }
        });

    }
}
