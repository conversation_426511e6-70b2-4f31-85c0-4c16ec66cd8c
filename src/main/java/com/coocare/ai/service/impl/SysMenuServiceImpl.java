package com.coocare.ai.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocare.ai.config.constants.CacheConstants;
import com.coocare.ai.entity.sys.SysMenu;
import com.coocare.ai.entity.sys.SysRoleMenu;
import com.coocare.ai.entity.sys.vo.SysMenuVO;
import com.coocare.ai.enums.MenuTypeEnum;
import com.coocare.ai.mapper.SysMenuMapper;
import com.coocare.ai.mapper.SysRoleMenuMapper;
import com.coocare.ai.service.ISysMenuService;
import com.coocare.ai.utils.PageDomain;
import com.github.yulichang.base.MPJBaseServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 菜单权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Service
@RequiredArgsConstructor
public class SysMenuServiceImpl extends MPJBaseServiceImpl<SysMenuMapper, SysMenu> implements ISysMenuService {

    private final SysRoleMenuMapper sysRoleMenuMapper;

    @Override
    public Object pageInfo(PageDomain pageDomain, String name) {
        var query = Wrappers.lambdaQuery(SysMenu.class);
        query.like(ObjectUtil.isNotEmpty(name), SysMenu::getName, name);
        return this.baseMapper.selectPage(new Page<>(pageDomain.getPageNo(), pageDomain.getPageSize()), query);
    }

    @Override
    public Object delById(Long id) {
        var query = Wrappers.lambdaQuery(SysMenu.class);
        query.eq(SysMenu::getParentId, id);
        var list = this.baseMapper.selectList(query);
        if (ObjectUtil.isNotEmpty(list)) {
            throw new RuntimeException("存在子菜单无法删除");
        }
        return removeById(id);
    }

    @Override
    public Object addEntity(SysMenu dto) {
        checkMenu(dto);
        this.baseMapper.insert(dto);
        return Boolean.TRUE;
    }

    @Override
    public Object tree(List<Long> menuIds) {
        if (null == menuIds || !menuIds.isEmpty()) {
            return getChildren(getSysMenuList(menuIds), 0L);
        }
        return Collections.EMPTY_LIST;
    }

    @Override
    public List<SysMenu> getSysMenuList(List<Long> menuIds) {
        var query = Wrappers.lambdaQuery(SysMenu.class);
        query.in(CollectionUtil.isNotEmpty(menuIds), SysMenu::getMenuId, menuIds);
        return this.baseMapper.selectList(query);
    }


    @Override
    @Cacheable(value = CacheConstants.ROLE_MENU, key = "#roleId", unless = "#result.isEmpty()")
    public List<Long> getMenuByRole(Long roleId) {
        var query = Wrappers.lambdaQuery(SysRoleMenu.class);
        query.eq(SysRoleMenu::getRoleId, roleId);
        return sysRoleMenuMapper.selectList(query).stream().map(SysRoleMenu::getMenuId).collect(Collectors.toList());
    }

    /***
     * 获取子菜单
     */
    private List<SysMenuVO> getChildren(List<SysMenu> all, Long pid) {
        return all.stream()
                .filter(item -> item.getParentId().equals(pid))
                .sorted(Comparator.comparing(SysMenu::getSortOrder))
                .map(item -> {
                    SysMenuVO vo = new SysMenuVO();
                    BeanUtils.copyProperties(item, vo);
                    vo.setChildren(getChildren(all, item.getMenuId()));
                    return vo;
                }).collect(Collectors.toList());
    }

    /**
     * 约束上下级
     */
    private void checkMenu(SysMenu dto) {
        SysMenu sysMenu = this.baseMapper.selectById(dto.getParentId());
        Optional.ofNullable(sysMenu).ifPresent(data -> {
            List<MenuTypeEnum> list = Arrays.stream(MenuTypeEnum.values())
                    .sorted(Comparator.comparing(MenuTypeEnum::getType))
                    .collect(Collectors.toList());
            list = list.stream().filter(item -> item.getType() < dto.getMenuType().getType()).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(list)) {
                MenuTypeEnum last = Collections.max(list);
                if (ObjectUtil.isNotNull(last)) {
                    if (!sysMenu.getMenuType().equals(last)) {
                        throw new RuntimeException("当前上级必须为" + last.getDescription());
                    }
                }
            } else {
                dto.setParentId(0L);
            }
        });
    }

}
