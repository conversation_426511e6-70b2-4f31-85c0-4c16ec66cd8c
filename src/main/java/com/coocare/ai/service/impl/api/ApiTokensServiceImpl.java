package com.coocare.ai.service.impl.api;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocare.ai.entity.api.ApiTokens;
import com.coocare.ai.mapper.api.ApiTokensMapper;
import com.coocare.ai.service.api.ApiTokensService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/2/14
 */

@DS("slave")
@Service
public class ApiTokensServiceImpl extends ServiceImpl<ApiTokensMapper, ApiTokens> implements ApiTokensService {

}
