package com.coocare.ai.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocare.ai.entity.AppCategory;
import com.coocare.ai.entity.AppCategoryInfo;
import com.coocare.ai.service.AppCategoryInfoService;
import com.coocare.ai.utils.EmptyUtil;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.coocare.ai.entity.AppInfo;
import com.coocare.ai.mapper.AppInfoMapper;
import com.coocare.ai.service.AppInfoService;

@Service
@RequiredArgsConstructor
public class AppInfoServiceImpl extends MPJBaseServiceImpl<AppInfoMapper, AppInfo> implements AppInfoService{

    private final AppCategoryInfoService appCategoryInfoService;

    @Override
    public PageUtils getAppListByCategoryId(Long categoryId, PageDomain pageDomain) {
        MPJLambdaWrapper<AppInfo> wrapper = JoinWrappers.lambda(AppInfo.class).selectAll(AppInfo.class)
                .leftJoin(AppCategoryInfo.class, AppCategoryInfo::getInfoId, AppInfo::getAppId)
                .eq(AppCategoryInfo::getCategoryId, categoryId).orderByAsc(AppInfo::getSort);
        return new PageUtils(selectJoinListPage(new Page<>(pageDomain.getPageNo(), pageDomain.getPageSize()), AppInfo.class, wrapper));
    }

    @Override
    public PageUtils getAppList(String name, String type, PageDomain pageDomain) {
        return new PageUtils(page(new Page<>(pageDomain.getPageNo(), pageDomain.getPageSize()), Wrappers.lambdaQuery(AppInfo.class).eq(EmptyUtil.isNotEmpty(name), AppInfo::getName, name)
        .eq(EmptyUtil.isNotEmpty(type), AppInfo::getType, type)
                .orderByAsc(AppInfo::getSort)));
    }

    @Override
    public Map<String, List<AppInfo>> getAppIndex() {
        MPJLambdaWrapper<AppInfo> wrapper = JoinWrappers.lambda(AppInfo.class)
                .selectAll(AppInfo.class)
                .selectAs(AppCategory::getCategoryName, AppInfo::getCategoryName)
                .selectAs(AppCategory::getCategoryId, AppInfo::getCategoryId)
                .leftJoin(AppCategoryInfo.class, AppCategoryInfo::getInfoId, AppInfo::getAppId)
                .leftJoin(AppCategory.class, AppCategory::getCategoryId, AppCategoryInfo::getCategoryId)
                .orderByAsc(AppInfo::getSort).orderByAsc(AppCategory::getSort);
        List<AppInfo> appList = selectJoinList(AppInfo.class, wrapper);
        return appList.stream().collect(Collectors.groupingBy(AppInfo::getCategoryName, LinkedHashMap::new, Collectors.toList()));
    }

    @Override
    public void assignCategory(List<Long> appIds, Long categoryId) {
        appIds.forEach(appId -> {
            long count = appCategoryInfoService.count(Wrappers.lambdaQuery(AppCategoryInfo.class)
                    .eq(AppCategoryInfo::getInfoId, appId)
                    .eq(AppCategoryInfo::getCategoryId, categoryId));
            if (count == 0) {
                AppCategoryInfo appCategoryInfo = new AppCategoryInfo();
                appCategoryInfo.setCategoryId(categoryId);
                appCategoryInfo.setInfoId(appId);
                appCategoryInfoService.save(appCategoryInfo);
            }
        });
    }
}
