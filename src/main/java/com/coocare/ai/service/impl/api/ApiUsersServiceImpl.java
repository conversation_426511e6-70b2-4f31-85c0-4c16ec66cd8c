package com.coocare.ai.service.impl.api;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocare.ai.entity.api.ApiUsers;
import com.coocare.ai.mapper.api.ApiUsersMapper;
import com.coocare.ai.service.api.ApiUsersService;
import com.coocare.ai.utils.EmptyUtil;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/2/14
 */

@DS("slave")
@Service
public class ApiUsersServiceImpl extends ServiceImpl<ApiUsersMapper, ApiUsers> implements ApiUsersService {


    public PageUtils queryPage(String username, PageDomain pageDomain) {
        IPage<ApiUsers> page = this.page(new Page<>(pageDomain.getPageNo(), pageDomain.getPageSize()), Wrappers.lambdaQuery(ApiUsers.class).eq(EmptyUtil.isNotEmpty(username), ApiUsers::getUsername, username).ne(ApiUsers::getGroup, "内部测试"));
        return new PageUtils(page);
    }
}
