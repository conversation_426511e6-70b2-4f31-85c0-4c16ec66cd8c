package com.coocare.ai.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocare.ai.entity.PaymentNotify;
import com.coocare.ai.mapper.PaymentNotifyMapper;
import com.coocare.ai.service.PaymentNotifyService;
import org.springframework.stereotype.Service;

@Service
public class PaymentNotifyServiceImpl extends ServiceImpl<PaymentNotifyMapper, PaymentNotify> implements PaymentNotifyService {

    @Override
    public PaymentNotify saveNotifyRecord(String channel, String deviceSn, Long price, Long timestamp, String sku) {
        PaymentNotify notify = new PaymentNotify()
                .setChannel(channel)
                .setDeviceSn(deviceSn)
                .setPrice(price)
                .setTimestamp(timestamp)
                .setSku(sku)
                .setStatus(0);
        save(notify);
        return notify;
    }

    @Override
    public void updateStatus(Long id, Integer status, String errorMsg) {
        PaymentNotify notify = new PaymentNotify()
                .setId(id)
                .setStatus(status)
                .setErrorMsg(errorMsg);
        updateById(notify);
    }
}