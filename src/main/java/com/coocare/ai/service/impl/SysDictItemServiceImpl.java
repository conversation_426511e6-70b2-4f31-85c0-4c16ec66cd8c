package com.coocare.ai.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocare.ai.entity.sys.SysDictItem;
import com.coocare.ai.mapper.SysDictItemMapper;
import com.coocare.ai.service.ISysDictItemService;
import com.coocare.ai.utils.EmptyUtil;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 字典项 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@Service
public class SysDictItemServiceImpl extends ServiceImpl<SysDictItemMapper, SysDictItem> implements ISysDictItemService {

    @Override
    @CacheEvict(value = "dict_details", key = "#itemValue")
    public SysDictItem getByItemValue(String itemValue) {
        return getOne(Wrappers.lambdaQuery(SysDictItem.class)
                .eq(SysDictItem::getItemValue, itemValue));
    }

    @Override
    public PageUtils queryDictItemPage(Long dictId, String description, PageDomain pageDomain) {
        return new PageUtils(page(new Page<>(pageDomain.getPageNo(), pageDomain.getPageSize()), Wrappers.lambdaQuery(SysDictItem.class)
                .eq(SysDictItem::getDictId, dictId)
                .like(EmptyUtil.isNotEmpty(description), SysDictItem::getDescription, description)));
    }
}
