package com.coocare.ai.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.service.IService;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.entity.sys.SysMenu;
import com.coocare.ai.utils.PageDomain;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 菜单权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
public interface ISysMenuService extends IService<SysMenu> {
    /**
     * 获取分页信息
     *
     * @param pageDomain
     * @param name
     * @return
     */
    Object pageInfo(PageDomain pageDomain, String name);

    /**
     * 删除菜单
     *
     * @param id
     * @return
     */
    Object delById(Long id);

    /**
     * 添加菜单
     *
     * @param dto
     * @return
     */
    Object addEntity(SysMenu dto);

    /**
     * 获取业务菜单树
     *
     * @return
     */
    Object tree(List<Long> menuIds);

    /**
     * 获取菜单列表
     *
     * @param menuIds
     * @return
     */
    List<SysMenu> getSysMenuList(List<Long> menuIds);

    /**
     * 获取角色对应的菜单
     *
     * @param roleId
     * @return
     */
    List<Long> getMenuByRole(Long roleId);

}
