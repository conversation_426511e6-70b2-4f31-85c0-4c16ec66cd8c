package com.coocare.ai.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.entity.AiCustomer;
import com.coocare.ai.entity.api.ApiTokens;
import com.coocare.ai.entity.api.ApiUsers;
import com.coocare.ai.service.AiCustomerService;
import com.coocare.ai.service.api.ApiTokensService;
import com.coocare.ai.service.api.ApiUsersService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/2/14
 */

@Tag(name = "开放接口")
@RestController
@RequestMapping("/open")
@RequiredArgsConstructor
@Slf4j
public class OpenController {

    private final AiCustomerService customerService;
    private final ApiUsersService apiUsersService;
    private final ApiTokensService apiTokensService;

    @SaIgnore
    @PostMapping("/push")
    public AjaxResult<?> deviceActivate(@RequestBody JSONObject data) {
        String sn = data.getString("sn");
        String pn = data.getString("pn");
        Integer days = data.getInteger("days");

        AiCustomer customer = customerService.getOne(Wrappers.lambdaQuery(AiCustomer.class)
                .eq(AiCustomer::getDeviceSn, sn)
                .eq(AiCustomer::getDevicePn, pn));

        ApiUsers apiUsers = apiUsersService.getOne(Wrappers.lambdaQuery(ApiUsers.class)
                .eq(ApiUsers::getUsername, sn));

        String key = checkToken(RandomUtil.randomString(48));

        if (customer == null) {
            if (apiUsers == null) {
                apiUsers = new ApiUsers();
                apiUsers.setUsername(sn);
                apiUsers.setPassword(pn);
                apiUsers.setQuota(10000L);
                apiUsersService.save(apiUsers);
            }
            ApiTokens tokens = new ApiTokens();
            tokens.setUserId(apiUsers.getId());
            tokens.setKey(key);
            tokens.setName(sn);
            tokens.setRemainQuota(500000L);
            tokens.setModelLimitsEnabled(false);
            tokens.setUnlimitedQuota(true);
            tokens.setExpiredTime(-1L);
            apiTokensService.save(tokens);
            customer = new AiCustomer();
            customer.setDeviceSn(sn);
            customer.setDevicePn(pn);
            customer.setExpiredTime(LocalDateTime.now().plusDays(days));
            customer.setAiToken("sk-" + key);
            customerService.save(customer);
        } else {
            customer.setExpiredTime(customer.getExpiredTime().plusDays(days));
            customerService.updateById(customer);
        }
        return AjaxResult.success();
    }

    private String checkToken(String token) {
        ApiTokens tokens = apiTokensService.getOne(Wrappers.lambdaQuery(ApiTokens.class)
                .eq(ApiTokens::getKey, token));
        if (tokens != null) {
            return checkToken(RandomUtil.randomString(49));
        }
        return token;
    }

}
