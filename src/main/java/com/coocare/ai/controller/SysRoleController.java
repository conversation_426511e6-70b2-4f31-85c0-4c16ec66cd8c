package com.coocare.ai.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.coocare.ai.config.constants.CacheConstants;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.config.plugins.logging.annotation.Logging;
import com.coocare.ai.entity.sys.SysRole;
import com.coocare.ai.service.ISysMenuService;
import com.coocare.ai.service.ISysRoleService;
import com.coocare.ai.utils.PageDomain;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 系统角色表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Tag(name = "角色管理")
@RestController
@RequestMapping("/sys/role")
@RequiredArgsConstructor
public class SysRoleController {

    private final ISysRoleService sysRoleService;
    private final ISysMenuService sysMenuService;

    /**
     * 分页查询角色信息
     * @param pageDomain 分页对象
     * @param name 查询条件
     * @return 分页对象
     */
    @Schema(description = "获取列表（分页）")
    @GetMapping("page")
    public AjaxResult<?> pageInfo(PageDomain pageDomain, String name) {
        return AjaxResult.success(sysRoleService.pageInfo(pageDomain, name));
    }

    /**
     * 添加角色
     * @param sysRole 角色信息
     * @return success、false
     */
    @Logging("添加角色")
    @Schema(description = "添加")
    @PostMapping
//    @PreAuthorize("@pms.hasPermission('sys_role_add')")
    @CacheEvict(value = CacheConstants.ROLE_DETAILS, allEntries = true)
    public AjaxResult<?> save(@Valid @RequestBody SysRole sysRole) {
        return AjaxResult.success(sysRoleService.save(sysRole));
    }

    /**
     * 修改角色
     * @param sysRole 角色信息
     * @return success/false
     */
    @Logging("修改角色")
    @PutMapping
//    @PreAuthorize("@pms.hasPermission('sys_role_edit')")
    @CacheEvict(value = CacheConstants.ROLE_DETAILS, allEntries = true)
    public AjaxResult<?> update(@RequestBody SysRole sysRole) {
        return AjaxResult.success(sysRoleService.updateById(sysRole));
    }

    /**
     * 删除角色
     * @param ids
     * @return
     */
    @Logging("删除角色")
    @DeleteMapping
//    @PreAuthorize("@pms.hasPermission('sys_role_del')")
    @CacheEvict(value = CacheConstants.ROLE_DETAILS, allEntries = true)
    public AjaxResult<?> removeById(@RequestBody Long[] ids) {
        return AjaxResult.ok(sysRoleService.removeRoleByIds(ids));
    }

    /**
     * 通过ID查询角色信息
     * @param id ID
     * @return 角色信息
     */
    @Schema(description = "删除")
    @DeleteMapping("/details/{id}")
    public AjaxResult<?> del(@PathVariable("id") Long id) {
        return AjaxResult.success(sysRoleService.delById(id));
    }

    @Schema(description = "状态切换")
    @PutMapping("/change/{id}/{status}")
    public AjaxResult<?> change(@PathVariable("id") Long id, @PathVariable("status") Boolean status) {
        return AjaxResult.success(sysRoleService.change(id, status));
    }

    @Schema(description = "角色分配菜单")
    @PutMapping("/assignMenu")
    public AjaxResult<?> assignMenu(Long roleId, List<Long> menuIds) {
        return AjaxResult.success(sysRoleService.assignMenu(roleId, menuIds));
    }

    /**
     * 获取角色列表
     * @return 角色列表
     */
    @GetMapping("/list")
    public AjaxResult<?> listRoles() {
        return AjaxResult.ok(sysRoleService.list(Wrappers.emptyWrapper()));
    }

    /**
     * 通过角色ID 查询角色列表
     * @param roleIdList 角色ID
     * @return
     */
    @PostMapping("/getRoleList")
    public AjaxResult<?> getRoleList(@RequestBody List<Long> roleIdList) {
        return AjaxResult.ok(sysRoleService.findRolesByRoleIds(roleIdList, CollUtil.join(roleIdList, StrUtil.UNDERLINE)));
    }

}
