package com.coocare.ai.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.sign.template.SaSignMany;
import cn.dev33.satoken.sign.template.SaSignUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSONObject;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.entity.PaymentNotify;
import com.coocare.ai.service.AiCustomerService;
import com.coocare.ai.service.PaymentNotifyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @description: 三方的回调接口
 * @author: Adam
 * @create: 2025-08-01 16:40
 **/
@SaIgnore
@Slf4j
@RestController
@RequestMapping("/notify")
@RequiredArgsConstructor
@Tag(name = "支付回调接口")
public class NotifyController {

    private final AiCustomerService customerService;
    private final PaymentNotifyService paymentNotifyService;

    @Operation(summary = "支付成功回调")
    @PostMapping("/{channel}")
    public AjaxResult<?> paymentNotify(@PathVariable String channel, String sn, Long price, Long timestamp, String sku, String nonce, String sign) {

        SaSignMany.getSignTemplate(channel).checkRequest(SaHolder.getRequest());
        paymentNotifyService.saveNotifyRecord(
                channel, sn, price, timestamp, sku
        );

        return AjaxResult.ok();
    }


    @GetMapping("test")
    public String get() {
        Map<String, Object> paramMap = new LinkedHashMap<>();
        paramMap.put("sn", "abcd");
        paramMap.put("orderSn", 1000);
        paramMap.put("price", 1000);
        paramMap.put("timestamp", System.currentTimeMillis());
        paramMap.put("sku", "abcd");
        paramMap.put("nonce", IdUtil.fastSimpleUUID());
        String paramStr = SaSignMany.getSignTemplate("qiangu").addSignParamsAndJoin(paramMap);
        System.out.println(paramStr);
        return paramStr;
    }

    public static void main(String[] args) {
        System.out.println(Instant.now().getEpochSecond());
    }
}