package com.coocare.ai.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.SecureUtil;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.config.oss.OssProperties;
import com.coocare.ai.config.oss.OssTemplate;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/2/12
 */

@RestController
@RequiredArgsConstructor
public class CommonController {

    private final OssTemplate ossTemplate;
    private final OssProperties ossProperties;

    @SaIgnore
    @Operation(summary = "获取OSS信息")
    @GetMapping("/plugins")
    public AjaxResult<?> getOssConfig(@RequestParam String filename) {
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        return AjaxResult.success(ossProperties.getCustomDomain() + filename + "?auth_key=" + timestamp + "-0-0-" + SecureUtil.md5("/" + filename + "-" + timestamp + "-0-0-coocarezhanai"));
    }


    @Operation(summary = "获取上传参数")
    @GetMapping("/upload")
    public AjaxResult<?> upload(@Parameter(name = "filename", description = "上传图片的名称。<br>接口返回真实的上传地址，客户端再通过真实上传地址通过PUT方式上传。<br>上传后URL访问形式为：https://oss.coocare.com/chat/当前日期(yyyymmdd)/文件名，客户端可自己组合URL后使用", required = true)
                                @RequestParam(value = "filename") String filename) {
        //如果要传到某个路径下，需要将这个路径+filename都作为key生成预上传url。
        //这样客户端在上传的时候，文件就会上传到对应的文件夹路径下
        //聊天上传文件的存放路径，暂统一定为 chat/yyyymmdd/文件名。这样上传后的访问路径客户端可以自己组合：https://oss.coocare.com/chat/yyyymmdd/文件名
        String path = "chat/" + DateUtil.today() + "/" + filename;
        return AjaxResult.success(ossTemplate.preUploadUrl(ossProperties.getBucketName(), path));
    }
}
