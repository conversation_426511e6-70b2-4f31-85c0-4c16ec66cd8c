package com.coocare.ai.controller;


import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.entity.AppCategory;
import com.coocare.ai.entity.AppInfo;
import com.coocare.ai.service.AppCategoryService;
import com.coocare.ai.service.AppInfoService;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.StringUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * @description: 应用管理
 * @author: Adam
 * @create: 2025-07-04 10:02
 **/

@Tag(name = "应用管理")
@RequiredArgsConstructor
@Slf4j
@RestController
@RequestMapping("/sys/app")
public class AppController {

    private final AppInfoService appInfoService;
    private final AppCategoryService appCategoryService;

    @Operation(summary = "获取应用分类列表")
    @GetMapping("category")
    public AjaxResult<?> getCategoryList() {
        return AjaxResult.ok(appCategoryService.getCategoryList());
    }

    @Operation(summary = "保存应用分类")
    @PostMapping("category")
    public AjaxResult<?> saveCategory(@RequestBody AppCategory appCategory) {
        return AjaxResult.ok(appCategoryService.save(appCategory));
    }

    @Operation(summary = "更新应用分类")
    @PutMapping("category")
    public AjaxResult<?> updateCategory(@RequestBody AppCategory appCategory) {
        return AjaxResult.ok(appCategoryService.updateById(appCategory));
    }

    @Operation(summary = "删除应用分类")
    @DeleteMapping("category/{ids}")
    public AjaxResult<?> removeCategory(@PathVariable String ids) {
        return AjaxResult.ok(appCategoryService.removeBatchByIds(StringUtil.parseLong(ids)));
    }

    @Operation(summary = "根据应用分类获取应用列表")
    @GetMapping("infoList/{categoryId}")
    public AjaxResult<?> getAppListByCategoryId(@PathVariable Long categoryId, PageDomain pageDomain) {
        return AjaxResult.ok(appInfoService.getAppListByCategoryId(categoryId, pageDomain));
    }

    @Operation(summary = "获取应用列表")
    @GetMapping("info")
    public AjaxResult<?> getAppList(String name, String type, PageDomain pageDomain) {
        return AjaxResult.ok(appInfoService.getAppList(name, type, pageDomain));
    }

    @Operation(summary = "保存应用")
    @PostMapping("info")
    public AjaxResult<?> saveAppInfo(@RequestBody AppInfo appInfo) {
        return AjaxResult.ok(appInfoService.save(appInfo));
    }

    @Operation(summary = "更新应用")
    @PutMapping("info")
    public AjaxResult<?> updateAppInfo(@RequestBody AppInfo appInfo) {
        return AjaxResult.ok(appInfoService.updateById(appInfo));
    }

    @Operation(summary = "删除应用")
    @DeleteMapping("info/{ids}")
    public AjaxResult<?> removeAppInfo(@PathVariable String ids) {
        return AjaxResult.ok(appInfoService.removeBatchByIds(StringUtil.parseLong(ids)));
    }

    @Operation(summary = "应用绑定分类")
    @PutMapping("assignCategory")
    public AjaxResult<?> assignCategory(String appIds, Long categoryId) {
        appInfoService.assignCategory(StringUtil.parseLong(appIds), categoryId);
        return AjaxResult.ok();
    }

}
