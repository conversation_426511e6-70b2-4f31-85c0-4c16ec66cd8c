package com.coocare.ai.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.coocare.ai.auth.support.StpKit;
import com.coocare.ai.auth.support.StpKit;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.entity.AiCustomer;
import com.coocare.ai.entity.AppInfo;
import com.coocare.ai.entity.api.ApiLogs;
import com.coocare.ai.entity.api.ApiUsers;
import com.coocare.ai.service.AiCustomerService;
import com.coocare.ai.service.AppCategoryService;
import com.coocare.ai.service.AppInfoService;
import com.coocare.ai.service.api.ApiLogsService;
import com.coocare.ai.service.api.ApiUsersService;
import com.coocare.ai.utils.AiUtils;
import com.coocare.ai.utils.EmptyUtil;
import com.coocare.ai.utils.PageDomain;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Map;
import java.util.Objects;

@SaIgnore
@Tag(name = "客户端接口")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/client")
public class ClientRestController {

    private final AiCustomerService customerService;
    private final ApiUsersService apiUsersService;
    private final ApiLogsService apiLogsService;
    private final AppInfoService appInfoService;
    private final AppCategoryService appCategoryService;
    private final AiUtils aiUtils;
    private final StringRedisTemplate redisTemplate;

    @Operation(summary = "登录")
    @PostMapping("/check")
    public AjaxResult<?> deviceLogin(@RequestBody JSONObject data) {
        return AjaxResult.success(customerService.deviceLogin(data));
    }

    @Operation(summary = "登录")
    @PostMapping("/active")
    public AjaxResult<?> deviceActive(@RequestBody JSONObject data) {
        return AjaxResult.success(customerService.deviceActive(data));
    }

    @Operation(summary = "签到")
    @PostMapping("/sign")
    public AjaxResult<?> signToday() {
        AiCustomer customer = customerService.getById(StpKit.CUSTOMER.getLoginIdAsLong());
        if (customer.getUpdateTime() != null && LocalDateTimeUtil.isSameDay(customer.getUpdateTime(), LocalDateTime.now())) {
            return AjaxResult.failed("already.singed");
        }
        if (customer.getCreateTime() != null && ChronoUnit.DAYS.between(customer.getCreateTime().toLocalDate(), LocalDateTime.now().toLocalDate()) > 31) {
            return AjaxResult.failed("gift.expired");
        }
        ApiUsers apiUsers = apiUsersService.getOne(Wrappers.<ApiUsers>lambdaQuery().eq(ApiUsers::getUsername, customer.getDeviceSn()));
        apiUsers.setQuota(apiUsers.getQuota() + 30000);
        apiUsersService.updateById(apiUsers);
        customer.setUpdateTime(LocalDateTime.now());
        customerService.updateById(customer);
        String signList = redisTemplate.opsForValue().get("sign:" + StpKit.CUSTOMER.getLoginIdAsString());
        String signToday = null;
        if (signList != null) {
            signToday = signList.substring(0, signList.length() - 1) + "1";
        }
        if (signToday != null) {
            redisTemplate.opsForValue().set("sign:" + StpKit.CUSTOMER.getLoginIdAsString(), signToday);
        }
        ApiLogs apiLogs = new ApiLogs();
        apiLogs.setUserId(apiUsers.getId());
        apiLogs.setUsername(apiUsers.getUsername());
        apiLogs.setQuota(30000L);
        apiLogs.setContent("用户签到, 赠送 30000 点额度");
        apiLogs.setType(4L);
        apiLogs.setCreatedAt(Instant.now().getEpochSecond());
        apiLogsService.save(apiLogs);
        return AjaxResult.success(signToday);
    }

    @Operation(summary = "签到记录")
    @GetMapping("/signList")
    public AjaxResult<?> signList() {
        return AjaxResult.success(redisTemplate.opsForValue().get("sign:" + StpKit.CUSTOMER.getLoginIdAsString()));
    }

    @Operation(summary = "代币查询接口")
    @GetMapping("/token")
    public AjaxResult<?> token() {
        AiCustomer customer = customerService.getById(StpKit.CUSTOMER.getLoginIdAsLong());
        ApiUsers apiUsers = apiUsersService.getOne(Wrappers.<ApiUsers>lambdaQuery().eq(ApiUsers::getUsername, customer.getDeviceSn()));
        return AjaxResult.success(apiUsers.getQuota());
    }

    @Operation(summary = "兑换")
    @PostMapping("/topup")
    public AjaxResult<?> topup(@RequestBody JSONObject data) {
        AiCustomer customer = customerService.getById(StpKit.CUSTOMER.getLoginIdAsLong());
        ApiUsers users = apiUsersService.getOne(Wrappers.<ApiUsers>lambdaQuery().eq(ApiUsers::getUsername, customer.getDeviceSn()));
        return AjaxResult.success(aiUtils.topup(data.getString("key"), users.getId() + "", customer.getDeviceSn()));
    }

    @Operation(summary = "使用记录")
    @GetMapping("/logs")
    public AjaxResult<?> logs(PageDomain pageDomain) {
        AiCustomer customer = customerService.getById(StpKit.CUSTOMER.getLoginIdAsLong());
        ApiUsers users = apiUsersService.getOne(Wrappers.<ApiUsers>lambdaQuery().eq(ApiUsers::getUsername, customer.getDeviceSn()));
        return AjaxResult.success(apiLogsService.queryPage(users.getId(), pageDomain));
    }

    @Operation(summary = "获取应用分类列表")
    @GetMapping("/app/category")
    public AjaxResult<?> getAppCategory() {
        return AjaxResult.success(appCategoryService.getCategoryList());
    }

    @Operation(summary = "根据应用分类获取应用列表")
    @GetMapping("/app/category/{categoryId}")
    public AjaxResult<?> getAppInfoList(@PathVariable Long categoryId, PageDomain pageDomain) {
        return AjaxResult.success(appInfoService.getAppListByCategoryId(categoryId, pageDomain));
    }

    @Operation(summary = "获取应用信息")
    @GetMapping("/app/info/{infoId}")
    public AjaxResult<?> getAppInfoById(@PathVariable Long infoId) {
        return AjaxResult.success(appInfoService.getById(infoId));
    }

    @SaIgnore
    @Operation(summary = "获取应用首页数据")
    @GetMapping("/app/index")
    public AjaxResult<?> getAppIndex() {
        return AjaxResult.success(appInfoService.getAppIndex());
    }

    @Operation(summary = "应用消费 category: 消费分类 category = model 模型下载, type = app 应用内购, type : 是否为校验,  body sku:xxxx")
    @PostMapping("/app/consumption/{category}/{type}")
    public AjaxResult<?> consumption(@PathVariable String category,
                                     @PathVariable String type,
                                     @RequestBody JSONObject data) {
        AiCustomer customer = customerService.getById(StpKit.CUSTOMER.getLoginIdAsLong());
        ApiUsers apiUsers = apiUsersService.getOne(Wrappers.<ApiUsers>lambdaQuery().eq(ApiUsers::getUsername, customer.getDeviceSn()));
        ApiLogs apiLogs = new ApiLogs();
        Long price = data.getLong("price");
        switch (category) {
            case "model":
                apiLogs = apiLogsService.getOne(Wrappers.lambdaQuery(ApiLogs.class).like(ApiLogs::getContent, data.getString("sku")).eq(ApiLogs::getUserId, apiUsers.getId()));
                if (EmptyUtil.isNotEmpty(apiLogs)) {
                    return AjaxResult.ok();
                }

                if (Objects.equals(type, "check")) {
                    return AjaxResult.failed(12004, "not.purchased");
                }

                if (apiUsers.getQuota() < price) {
                    return AjaxResult.failed(12002, "quota.not.enough");
                }
                apiLogs = new ApiLogs();
                apiUsers.setQuota(apiUsers.getQuota() - price);
                apiLogs.setQuota(price);
                apiLogs.setContent("模型" + data.getString("sku") + "解锁, 消耗 " + price + " 点额度");
                break;
            case "app":
                if (apiUsers.getQuota() < price) {
                    return AjaxResult.failed(12002, "quota.not.enough");
                }
                apiUsers.setQuota(apiUsers.getQuota() - price);
                apiLogs.setQuota(price);
                apiLogs.setContent("渠道" + data.getString("channel") + "消费 " + price + " 点额度");
                break;
            default:
                break;
        }
        apiUsersService.updateById(apiUsers);
        customer.setUpdateTime(LocalDateTime.now());
        customerService.updateById(customer);
        apiLogs.setUserId(apiUsers.getId());
        apiLogs.setUsername(apiUsers.getUsername());
        apiLogs.setType(6L);
        apiLogs.setGroup(apiUsers.getGroup());
        apiLogs.setCreatedAt(Instant.now().getEpochSecond());
        apiLogsService.save(apiLogs);
        return AjaxResult.success();
    }

}
