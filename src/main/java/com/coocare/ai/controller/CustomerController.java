package com.coocare.ai.controller;


import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.entity.api.ApiLogs;
import com.coocare.ai.entity.api.ApiUsers;
import com.coocare.ai.service.api.ApiLogsService;
import com.coocare.ai.service.api.ApiUsersService;
import com.coocare.ai.utils.EmptyUtil;
import com.coocare.ai.utils.PageDomain;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @description: 客户信息查询
 * @author: Adam
 * @create: 2025-04-25 15:20
 **/

@SaIgnore
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/customer")
public class CustomerController {

    private final ApiUsersService apiUsersService;
    private final ApiLogsService apiLogsService;

    @Operation(summary = "查询用户")
    @GetMapping("page")
    public AjaxResult<?> page(String username, PageDomain pageDomain) {
        return AjaxResult.ok(apiUsersService.queryPage(username, pageDomain));
    }

    @Operation(summary = "查询用户日志")
    @GetMapping("logs/{userId}")
    public AjaxResult<?> getUserList(@PathVariable Long userId, PageDomain pageDomain) {
        return AjaxResult.ok(apiLogsService.queryPage(userId, pageDomain));
    }


    @Operation(summary = "查询用户日志")
    @GetMapping("top")
    public AjaxResult<?> top() {
        return AjaxResult.ok(Map.of("memberCount", apiUsersService.count(Wrappers.lambdaQuery(ApiUsers.class).ne(ApiUsers::getGroup, "内部测试")),
                "usedQuota",  apiLogsService.getOne(Wrappers.query(ApiLogs.class).select("sum(quota) as allQuota")).getAllQuota(),
                "todayCount", apiLogsService.count(Wrappers.query(ApiLogs.class).eq("FROM_UNIXTIME(created_at,'%Y-%m-%d')", DateUtil.today())),
                "todayUsedQuota", apiLogsService.getOne(Wrappers.query(ApiLogs.class).select("sum(quota) as allQuota").eq("FROM_UNIXTIME(created_at,'%Y-%m-%d')", DateUtil.today())).getAllQuota()));
    }


}
