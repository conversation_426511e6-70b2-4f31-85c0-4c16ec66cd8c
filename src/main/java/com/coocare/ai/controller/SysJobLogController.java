//package com.coocare.ai.controller;
//
//
//import com.coocare.ai.config.domain.AjaxResult;
//import com.coocare.ai.entity.SysJobLog;
//import com.coocare.ai.service.ISysJobLogService;
//import com.coocare.ai.utils.PageDomain;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import lombok.RequiredArgsConstructor;
//import org.springframework.web.bind.annotation.*;
//
///**
// * <p>
// * 定时任务调度日志表 前端控制器
// * </p>
// *
// * <AUTHOR>
// * @since 2023-10-10
// */
//@Tag(name = "调度日志操作处理")
//@RestController
//@RequestMapping("/sys/job/log")
//@RequiredArgsConstructor
//public class SysJobLogController {
//
//    private final ISysJobLogService jobLogService;
//
//    /**
//     * 查询定时任务调度日志列表
//     */
//    @GetMapping("/list")
//    public AjaxResult list(PageDomain pageDomain, SysJobLog sysJobLog) {
//        return AjaxResult.success(jobLogService.selectJobLogPage(pageDomain, sysJobLog));
//    }
//
//
//    /**
//     * 根据调度编号获取详细信息
//     */
//    @GetMapping(value = "/{jobLogId}")
//    public AjaxResult getInfo(@PathVariable Long jobLogId) {
//        return AjaxResult.success(jobLogService.selectJobLogById(jobLogId));
//    }
//
//
//    /**
//     * 删除定时任务调度日志
//     */
//    @DeleteMapping("/{jobLogIds}")
//    public AjaxResult remove(@PathVariable Long[] jobLogIds) {
//        return AjaxResult.success(jobLogService.deleteJobLogByIds(jobLogIds));
//    }
//
//    /**
//     * 清空定时任务调度日志
//     */
//    @DeleteMapping("/clean")
//    public AjaxResult clean() {
//        jobLogService.cleanJobLog();
//        return AjaxResult.success();
//    }
//}
