//package com.coocare.ai.controller;
//
//
//import cn.dev33.satoken.stp.StpUtil;
//import com.coocare.ai.config.domain.AjaxResult;
//import com.coocare.ai.entity.SysJob;
//import com.coocare.ai.entity.SysUser;
//import com.coocare.ai.service.ISysJobService;
//import com.coocare.ai.utils.PageDomain;
//import com.coocare.ai.utils.ScheduleConstants;
//import com.coocare.ai.utils.quartz.CronUtils;
//import com.coocare.ai.utils.quartz.ScheduleUtils;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import jakarta.validation.Valid;
//import lombok.RequiredArgsConstructor;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//import java.util.Objects;
//
///**
// * <p>
// * 定时任务调度表 前端控制器
// * </p>
// *
// * <AUTHOR>
// * @since 2023-10-10
// */
//@Tag(name = "调度任务信息操作处理")
//@RestController
//@RequestMapping("/sys/job")
//@RequiredArgsConstructor
//public class SysJobController {
//
//    private final ISysJobService jobService;
//
//    @Operation(summary = "查询定时任务列表")
//    @GetMapping("/page")
//    public AjaxResult queryPage(PageDomain pageDomain, SysJob sysJob) {
//        return AjaxResult.success(jobService.selectJobPage(pageDomain, sysJob));
//    }
//
//    @Operation(summary = "获取定时任务详细信息")
//    @GetMapping(value = "/{jobId}")
//    public AjaxResult getInfo(@PathVariable("jobId") Long jobId) {
//        return AjaxResult.success(jobService.selectJobById(jobId));
//    }
//
//    @Operation(summary = "新增定时任务")
//    @PostMapping
//    public AjaxResult<Boolean> add(@Valid @RequestBody SysJob sysJob) throws Exception {
//        Long id = jobService.insertJob(sysJob);
//        return AjaxResult.success(!Objects.equals(id, null));
//    }
//
//    @Operation(summary = "修改定时任务")
//    @PutMapping
//    public AjaxResult edit(@RequestBody SysJob job) throws Exception {
//        if (!CronUtils.isValid(job.getCronExpression())) {
//            return AjaxResult.failed("修改任务'" + job.getJobName() + "'失败，Cron表达式不正确");
//        } else if (StringUtils.containsIgnoreCase(job.getInvokeTarget(), ScheduleConstants.LOOKUP_RMI)) {
//            return AjaxResult.failed("修改任务'" + job.getJobName() + "'失败，目标字符串不允许'rmi'调用");
//        } else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), new String[]{ScheduleConstants.LOOKUP_LDAP, ScheduleConstants.LOOKUP_LDAPS})) {
//            return AjaxResult.failed("修改任务'" + job.getJobName() + "'失败，目标字符串不允许'ldap(s)'调用");
////        } else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), new String[]{ScheduleConstants.HTTP, ScheduleConstants.HTTPS})) {
////            return AjaxResult.failed("修改任务'" + job.getJobName() + "'失败，目标字符串不允许'http(s)'调用");
//        } else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), ScheduleConstants.JOB_ERROR_STR)) {
//            return AjaxResult.failed("修改任务'" + job.getJobName() + "'失败，目标字符串存在违规");
//        } else if (!ScheduleUtils.whiteList(job.getInvokeTarget())) {
//            return AjaxResult.failed("修改任务'" + job.getJobName() + "'失败，目标字符串不在白名单内");
//        }
//        job.setUpdateBy(((SysUser) StpUtil.getSession().get("user")).getUsername());
//        return AjaxResult.success(jobService.updateJob(job));
//    }
//
//    @Operation(summary = "定时任务状态修改")
//    @PutMapping("/changeStatus")
//    public AjaxResult changeStatus(@RequestBody SysJob job) throws Exception {
//        SysJob newJob = jobService.selectJobById(job.getJobId());
//        newJob.setStatus(job.getStatus());
//        return AjaxResult.success(jobService.changeStatus(newJob));
//    }
//
//    @Operation(summary = "定时任务立即执行一次")
//    @PutMapping("/run")
//    public AjaxResult run(@RequestBody SysJob job) throws Exception {
//        boolean b = jobService.run(job);
//        return b ? AjaxResult.success() : AjaxResult.failed("任务不存在或已过期！");
//    }
//
//    @Operation(summary = "删除定时任务")
//    @DeleteMapping("/{jobIds}")
//    public AjaxResult remove(@PathVariable Long[] jobIds) throws Exception {
//        jobService.deleteJobByIds(jobIds);
//        return AjaxResult.success();
//    }
//
//    @Operation(summary = "查询cron表达式近5次的执行时间")
//    @GetMapping("/queryCronExpression")
//    public AjaxResult queryCronExpression(@RequestParam(value = "cronExpression", required = false) String cronExpression) {
//        if (jobService.checkCronExpressionIsValid(cronExpression)) {
//            List<String> dateList = CronUtils.getRecentTriggerTime(cronExpression);
//            return AjaxResult.success(dateList);
//        }
//        return AjaxResult.failed("表达式无效");
//    }
//}
