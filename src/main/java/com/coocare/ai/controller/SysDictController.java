package com.coocare.ai.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.entity.sys.SysDict;
import com.coocare.ai.entity.sys.SysDictItem;
import com.coocare.ai.service.ISysDictItemService;
import com.coocare.ai.service.ISysDictService;
import com.coocare.ai.utils.EmptyUtil;
import com.coocare.ai.utils.PageDomain;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 字典表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-11
 */

@Tag(name = "字典管理")
@RequiredArgsConstructor
@Slf4j
@RestController
@RequestMapping("/sys/dict")
public class SysDictController {

    private final ISysDictService dictService;
    private final ISysDictItemService dictItemService;

    @Operation(summary = "字典项目列表")
    @GetMapping("/page")
    public AjaxResult<?> queryDictPage(SysDict dict, PageDomain pageDomain) {
        return AjaxResult.ok(dictService.queryDictPage(dict, pageDomain));
    }


    @Operation(summary = "字典项目子项列表")
    @GetMapping("{dictId}/item/page")
    public AjaxResult<?> queryDictItemPage(@PathVariable Long dictId, String description, PageDomain pageDomain) {
        return AjaxResult.ok(dictItemService.queryDictItemPage(dictId, description, pageDomain));
    }

    @Operation(summary = "根据字典项目子项列表")
    @GetMapping("/{dictType}/item")
    public AjaxResult<?> queryDictItemByType(@PathVariable String dictType, String label) {
        return AjaxResult.ok(dictItemService.list(new LambdaQueryWrapper<SysDictItem>()
                .eq(SysDictItem::getDictType, dictType)
                .like(EmptyUtil.isNotEmpty(label), SysDictItem::getLabel, label)));
    }

    @Operation(summary = "保存字典项目")
    @PostMapping("/item")
    @CacheEvict(value = "dict_details", allEntries = true)
    public AjaxResult<?> editItem(@RequestBody SysDictItem dictItem) {
        return AjaxResult.ok(dictItemService.saveOrUpdate(dictItem));
    }

    @Operation(summary = "删除字典项目")
    @DeleteMapping("/item/{id}")
    @CacheEvict(value = "dict_details", allEntries = true)
    public AjaxResult<?> delItem(@PathVariable("id") Long id) {
        return AjaxResult.ok(dictItemService.removeById(id));
    }

    @Operation(summary = "保存字典")
    @PostMapping
    public AjaxResult<?> saveDict(@RequestBody SysDict dict) {
        return AjaxResult.ok(dictService.saveOrUpdate(dict));
    }

    @Operation(summary = "删除字典")
    @DeleteMapping("/{id}")
    public AjaxResult<?> delDict(@PathVariable("id") Long id) {
        return AjaxResult.ok(dictService.removeById(id));
    }
}
