package com.coocare.ai.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.coocare.ai.config.domain.AjaxResult;
import lombok.RequiredArgsConstructor;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.PromptChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.InMemoryChatMemory;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.image.ImagePrompt;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiImageModel;
import org.springframework.ai.openai.OpenAiImageOptions;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.net.URLEncoder;

import static org.springframework.ai.chat.client.advisor.AbstractChatMemoryAdvisor.CHAT_MEMORY_CONVERSATION_ID_KEY;
import static org.springframework.ai.chat.client.advisor.AbstractChatMemoryAdvisor.CHAT_MEMORY_RETRIEVE_SIZE_KEY;

/**
 * <AUTHOR>
 * @date 2025/2/14
 */

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1")
public class AiRestController {

    private final OpenAiImageModel aiImageModel;
    private final OpenAiChatModel aiChatModel;
    private final ChatMemory chatMemory = new InMemoryChatMemory();

    @PostMapping("createImage")
    public AjaxResult<?> createImage(String message) {
        OpenAiImageOptions zhiPuAiImageOptions = OpenAiImageOptions.builder().model("cogview-3-plus").build();
        return AjaxResult.success(aiImageModel.call(new ImagePrompt(message, zhiPuAiImageOptions)));
    }

    @GetMapping(value = "webSearch", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Object chat(String message) {
        String search = "https://www.baidu.com/s?tn=json&rn=2&wd=" + URLEncoder.encode(message);
        String res = HttpRequest.get(search).execute().body();
        JSONObject searchResult = new JSONObject(res);
        JSONArray results = searchResult.getJSONObject("feed").getJSONArray("entry");

        StringBuilder systemMessages = new StringBuilder("You are an AI model who is expert at searching the web and answering user's queries.\\n\\nGenerate a response that is informative and relevant to the user's query based on provided search results. the current date and time are " + DateUtil.now() +
                ".\\n\\n`search-results` block provides knowledge from the web search results. You can use this information to generate a meaningful response.\\n\\n<search-results>\\n ");
        for (int i = 0; i < results.size(); i++) {
            JSONObject result = (JSONObject) results.get(i);
            systemMessages.append("<result source=\\\"").append(result.getStr("url")).append("\" id=\"").append(result.getStr("pn")).append("\\\">").append(result.getStr("title")).append("</result>\\n");
        }
        systemMessages.append("</search-results>\\n");

        ChatClient chatClient = ChatClient.builder(aiChatModel)
                .defaultAdvisors(new PromptChatMemoryAdvisor(chatMemory))
                .build();
        //return AjaxResult.success(aiChatModel.call(new Prompt(new UserMessage(message))));
        return chatClient.prompt().system(systemMessages.toString())
                .user(message)
                .advisors(a -> a
                        .param(CHAT_MEMORY_CONVERSATION_ID_KEY, 123L)
                        .param(CHAT_MEMORY_RETRIEVE_SIZE_KEY, 100)
                )
                .advisors(new SimpleLoggerAdvisor())
                .stream().chatResponse();
    }

    @PostMapping(value = "/chat/completions", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatResponse> msg(String message) {
        var prompt = new Prompt(new UserMessage(message));
        return aiChatModel.stream(prompt);
    }


    @GetMapping(value = "chatWithChatMemory", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public void chatWithChatMemory(String message) {
        ChatClient chatClient = ChatClient.builder(aiChatModel)
                .defaultAdvisors(new PromptChatMemoryAdvisor(chatMemory))
                .build();
        chatClient.prompt()
                .user(message)
                .advisors(a -> a
                        .param(CHAT_MEMORY_CONVERSATION_ID_KEY, 123L)
                        .param(CHAT_MEMORY_RETRIEVE_SIZE_KEY, 100)
                )
                .advisors(new SimpleLoggerAdvisor())
                .stream();
    }

}
