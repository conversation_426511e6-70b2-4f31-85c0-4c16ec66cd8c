package com.coocare.ai.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.entity.sys.SysUser;
import com.coocare.ai.entity.sys.dto.UserDTO;
import com.coocare.ai.entity.sys.dto.UserRoleAssignDTO;
import com.coocare.ai.service.ISysUserService;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 用户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Tag(name = "用户管理")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/sys/user")
public class SysUserController {

    private final ISysUserService userService;

    @Operation(summary = "查询用户")
    @GetMapping()
//    @PreAuthorize("hasAuthority('SCOPE_message.write')")
    public AjaxResult<?> getUserList(String searchWord, Boolean status, PageDomain pageDomain) {
        PageUtils page = userService.queryPage(searchWord, status, pageDomain);
        return AjaxResult.ok(page);
    }

    /**
     * 保存
     */
    @Operation(summary = "保存用户", description = "请传入用户名/手机号/邮箱/密码")
    @PostMapping()
    public AjaxResult<?> saveUser(@RequestBody UserDTO userDTO) {
        SysUser sysUser = userService.queryUserByName(userDTO.getUsername());
        if (ObjectUtil.isNotNull(sysUser)) {
            return AjaxResult.failed("user.name.exist");
        }
        return AjaxResult.ok(userService.saveUser(userDTO));
    }

    /**
     * 修改
     */
    @Operation(summary = "修改用户")
    @PutMapping()
    public AjaxResult<?> updateUser(@RequestBody UserDTO userDTO) {
        return AjaxResult.ok(userService.updateUser(userDTO));
    }

    /**
     * 删除
     */
    @Operation(summary = "删除用户")
    @DeleteMapping("/{id}")
//    @SaCheckPermission("sys_user_del")
    public AjaxResult<?> deleteUser(@PathVariable Long id) {
        return AjaxResult.ok(userService.removeById(id));
    }

    @Operation(summary = "用户修改密码")
    @PutMapping("changePass")
    public AjaxResult<?> changePass(@RequestParam("oldPass") String oldPass, @RequestParam("password") String password) {
        if (!userService.changeUserPass(oldPass, password)) {
            return AjaxResult.failed("密码错误");
        } else {
            return AjaxResult.ok();
        }
    }

    /**
     * 校验密码接口
     * @param password 需要校验的密码
     * @return R 返回结果对象，包含校验密码操作的结果信息
     */
    @Operation(summary = "校验密码接口")
    @PostMapping("/check")
    public AjaxResult<?> check(String password) {
        return AjaxResult.success(userService.checkPassword(password));
    }

    @Operation(summary = "重置密码")
    @PutMapping("resetPass/{id}")
    @SaIgnore
    public AjaxResult<?> reset(@PathVariable("id") Long id) {
        return AjaxResult.ok(userService.resetPassword(id));
    }

    @Operation(summary = "状态切换")
    @PutMapping("/change/{id}/{status}")
    public AjaxResult<?> change(@PathVariable("id") Long id, @PathVariable("status") Boolean status) {
        return AjaxResult.success(userService.change(id, status));
    }

    @Operation(summary = "用户分配角色")
    @PutMapping("/assignRoles")
    public AjaxResult<?> assignRoles(@Valid @RequestBody UserRoleAssignDTO dto) {
        return AjaxResult.success(userService.assignRoles(dto));
    }

    @Operation(summary = "获取当前登录的用户信息")
    @GetMapping("/loginInfo")
    public AjaxResult<?> loginInfo() {
        return AjaxResult.ok(userService.userInfo(StpUtil.getLoginIdAsLong()));
    }


    @Operation(summary = "根据用户id获取角色信息")
    @GetMapping("/getRolesByUser/{id}")
    public AjaxResult<?> getRolesByUser(@PathVariable("id") Long id) {
        return AjaxResult.ok(userService.getRolesByUser(id));
    }
}
