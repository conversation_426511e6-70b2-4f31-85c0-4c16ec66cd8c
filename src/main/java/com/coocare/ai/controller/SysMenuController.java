package com.coocare.ai.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.config.plugins.logging.annotation.Logging;
import com.coocare.ai.entity.sys.SysMenu;
import com.coocare.ai.service.ISysMenuService;
import com.coocare.ai.utils.PageDomain;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 菜单权限表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Tag(name = "菜单管理")
@RestController
@RequestMapping("/sys/menu")
@RequiredArgsConstructor
public class SysMenuController {

    private final ISysMenuService sysMenuService;

    @Operation(summary = "获取列表（分页）")
    @GetMapping("page")
    public AjaxResult<?> pageInfo(PageDomain pageDomain, String name) {
        return AjaxResult.success(sysMenuService.pageInfo(pageDomain, name));
    }

    @Operation(summary = "添加")
    @PostMapping
    public AjaxResult<?> add(@Valid @RequestBody SysMenu dto) {
        return AjaxResult.success(sysMenuService.addEntity(dto));
    }

    @Operation(summary = "修改")
    @PutMapping
    public AjaxResult<?> update(@RequestBody SysMenu dto) {
        return AjaxResult.success(sysMenuService.updateById(dto));
    }

    @Operation(summary = "删除")
    @DeleteMapping("/{id}")
    public AjaxResult<?> del(@PathVariable("id") Long id) {
        return AjaxResult.success(sysMenuService.delById(id));
    }


    @Operation(summary = "获取业务菜单树")
    @GetMapping("tree")
    public AjaxResult<?> tree() {
        return AjaxResult.success(sysMenuService.tree(null));
    }

    @Operation(summary = "根据角色业务菜单树")
    @PostMapping("treeByRole")
    public AjaxResult<?> treeByRole(@RequestBody List<Long> roleIds) {
        var menuIds = new ArrayList<Long>();
        roleIds.forEach(roleId -> {
            menuIds.addAll(sysMenuService.getMenuByRole(roleId));
        });
        return AjaxResult.success(sysMenuService.tree(menuIds.stream().distinct().collect(Collectors.toList())));
    }

    @Operation(summary = "获取父级菜单")
    @GetMapping("/parent")
    public AjaxResult<?> parentMenu() {
        return AjaxResult.success(sysMenuService.list(new LambdaQueryWrapper<SysMenu>()
                .eq(SysMenu::getParentId, 0L)
                .orderByAsc(SysMenu::getSortOrder)));
    }
}
