server:
  port: 8070

spring:
  application:
    name: @artifactId@
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  main:
    banner-mode: off
    allow-bean-definition-overriding: true
    allow-circular-references: true
  messages:
    basename: i18n/messages
  cache:
    type: redis
  data:
    redis:
      host: ${REDIS_HOST:coocare-sh-extend.redis.rds.aliyuncs.com}
      port: ${REDIS_PORT:6379}
      database: ${REDIS_DATABASE:6}
      password: ${REDIS_PASSWORD:Pass@1234}
      timeout: 5000
  datasource:
    dynamic:
      datasource:
        # 主数据库
        master:
          url: jdbc:mysql://${MYSQL_HOST:coocare-sh-extend.mysql.rds.aliyuncs.com}:${MYSQL_PORT:3306}/${MYSQL_DATABASE:zhan_ai}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: ${MYSQL_USERNAME:ai}
          password: ${MYSQL_PASSWORD:X%p9T5%JGhbf}
        slave:
          url: jdbc:mysql://${API_MYSQL_HOST:coocare-sh-extend.mysql.rds.aliyuncs.com}:${API_MYSQL_PORT:3306}/${API_MYSQL_DATABASE:one_api}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: ${API_MYSQL_USERNAME:ai}
          password: ${API_MYSQL_PASSWORD:X%p9T5%JGhbf}
  ai:
    openai:
      api-key: sk-3c2bac4fb11942c688ec36ae620c016d
      base-url: https://dashscope.aliyuncs.com/compatible-mode/v1
      chat:
        enabled: true
        options:
          model: deepseek-r1
          max-tokens: 10240
          temperature: 0.7
        completions-path: /chat/completions
      image:
        enabled: true
        options:
          model: wanx2.1-t2i-turbo
        completions-path: /images/generations
    retry:
      backoff:
        initial-interval: 2000
        multiplier: 2
        max-interval: 5000
        on-client-errors: true
        exclude-on-http-codes: 400,401,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,421,422,423,424,426,428,429,431,451,500,501,502,503,504,505,506,507,508,510,511
      max-attempts: 3

# mybatis-plus 配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/*Mapper.xml
  global-config:
    banner: false
    db-config:
      id-type: auto
      where-strategy: not_empty
      insert-strategy: not_empty
      update-strategy: not_null
  configuration:
    call-setters-on-nulls: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

mybatis-plus-join:
  banner: false
  sub-table-logic: true
  ms-cache: true
  table-alias: t
  logic-del-type: where

springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  default-flat-param-object: true

knife4j:
  enable: false
  setting:
    enable-swagger-models: true
    enable-dynamic-parameter: false
    footer-custom-content: "<strong>Copyright ©️ 2025 Coocare.Inc. All Rights Reversed</strong>"
    enable-footer-custom: true
    enable-footer: true
    enable-document-manage: true
  # production: true  # 生产环境屏蔽swagger

# 阿里云oss配置 支持其他云存储
oss:
  enable: true
  accessKey: ${OSS_ACCESS_KEY:LTAI5t6Kmg87sfjpGR6oiNve}
  secretKey: ${OSS_SECRET_KEY:******************************}
  endpoint: ${OSS_ENDPOINT:oss-cn-shanghai.aliyuncs.com}
  bucketName: ${OSS_BUCKET_NAME:coocare-ai}
  custom-domain: ${OSS_CUSTOM_DOMAIN:https://plugins.coocare.com/}                   # 自定义域名
  path-style-access: false

# sa-token配置
sa-token:
  token-name: Authorization
  token-prefix: Bearer
  timeout: 2592000
  active-timeout: -1
  autoRenew: true
  is-concurrent: true
  is-share: true
  token-style: tik
  is-log: false
  is-print: false
  sign-many:
    qiangu:
      # API 接口签名秘钥 （随便乱摁几个字母即可）
      secret-key: kQwIOrYvnXmSDkwEiFngrKidMcdrgKor
      digest-algo: sha256

#logging:
#  level:
#    org:
#      springframework:
#        web:
#          filter:
#            CommonsRequestLoggingFilter: DEBUG

ai:
  server:
    key: ${NEW_API_KEY:p+******************************}
    id: ${NEW_API_ID:1}
    url: ${NEW_API_URL:https://ai.coocare.com}
