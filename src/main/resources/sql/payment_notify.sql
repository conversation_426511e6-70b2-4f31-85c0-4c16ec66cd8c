CREATE TABLE `payment_notify` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `channel` varchar(50) NOT NULL COMMENT '支付渠道',
  `device_sn` varchar(100) NOT NULL COMMENT '设备SN',
  `price` bigint NOT NULL COMMENT '支付金额(分)',
  `timestamp` bigint NOT NULL COMMENT '时间戳',
  `sku` varchar(100) NOT NULL COMMENT 'SKU',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '处理状态 0-待处理 1-处理成功 2-处理失败',
  `error_msg` text COMMENT '错误信息',
  `raw_data` json COMMENT '原始回调数据',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_device_sn` (`device_sn`),
  KEY `idx_channel` (`channel`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付回调记录表';