<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocare.ai.mapper.AppCategoryMapper">
  <resultMap id="BaseResultMap" type="com.coocare.ai.entity.AppCategory">
    <!--@mbg.generated-->
    <id column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    category_id, category_name, sort
  </sql>
</mapper>