<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocare.ai.mapper.AppCategoryInfoMapper">
  <resultMap id="BaseResultMap" type="com.coocare.ai.entity.AppCategoryInfo">
    <!--@mbg.generated-->
    <!--@Table app_category_info-->
    <result column="info_id" jdbcType="BIGINT" property="infoId" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    info_id, category_id
  </sql>
</mapper>