<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocare.ai.mapper.AiCustomerMapper">
  <resultMap id="BaseResultMap" type="com.coocare.ai.entity.AiCustomer">
    <!--@mbg.generated-->
    <!--@Table ai_customer-->
    <id column="customer_id" jdbcType="BIGINT" property="customerId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="device_sn" jdbcType="VARCHAR" property="deviceSn" />
    <result column="device_pn" jdbcType="VARCHAR" property="devicePn" />
    <result column="sys_token" jdbcType="VARCHAR" property="sysToken" />
    <result column="ai_token" jdbcType="VARCHAR" property="aiToken" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="expired_time" jdbcType="TIMESTAMP" property="expiredTime" />
    <result column="del_flag" jdbcType="VARCHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    customer_id, `name`, device_sn, device_pn, sys_token, ai_token, create_time, update_time, expired_time,
    del_flag
  </sql>
</mapper>
