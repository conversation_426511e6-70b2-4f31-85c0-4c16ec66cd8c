<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocare.ai.mapper.AppInfoMapper">
  <resultMap id="BaseResultMap" type="com.coocare.ai.entity.AppInfo">
    <!--@mbg.generated-->
    <!--@Table app_info-->
    <id column="app_id" jdbcType="BIGINT" property="appId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="info" jdbcType="VARCHAR" property="info" />
    <result column="tags" jdbcType="VARCHAR" property="tags" />
    <result column="detail" jdbcType="VARCHAR" property="detail" />
    <result column="intel_download_url" jdbcType="VARCHAR" property="intelDownloadUrl" />
    <result column="intel_core_requirement" jdbcType="INTEGER" property="intelCoreRequirement" />
    <result column="intel_memery_requirement" jdbcType="INTEGER" property="intelMemeryRequirement" />
    <result column="amd_download_url" jdbcType="VARCHAR" property="amdDownloadUrl" />
    <result column="amd_core_requirement" jdbcType="VARCHAR" property="amdCoreRequirement" />
    <result column="amd_memery_requirement" jdbcType="VARCHAR" property="amdMemeryRequirement" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="link" jdbcType="VARCHAR" property="link" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    app_id, `name`, `type`, icon, info, tags, detail, intel_download_url, intel_core_requirement, 
    intel_memery_requirement, amd_download_url, amd_core_requirement, amd_memery_requirement, 
    version, sort, update_time, link
  </sql>
</mapper>