package com.coocare.ai.controller;

import cn.dev33.satoken.sign.template.SaSignMany;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.service.AiCustomerService;
import com.coocare.ai.service.PaymentNotifyService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * NotifyController 单元测试类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-01
 */
@WebMvcTest(NotifyController.class)
@DisplayName("支付回调控制器测试")
class NotifyControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private AiCustomerService customerService;

    @MockBean
    private PaymentNotifyService paymentNotifyService;

    private static final String TEST_CHANNEL = "alipay";
    private static final String TEST_SN = "SN123456789";
    private static final Long TEST_PRICE = 1000L;
    private static final Long TEST_TIMESTAMP = 1672531200L;
    private static final String TEST_SKU = "PRODUCT_001";
    private static final String TEST_NONCE = "abc123def456";
    private static final String TEST_SIGN = "test_signature";

    @BeforeEach
    void setUp() {
        // 重置所有mock对象
        reset(customerService, paymentNotifyService);
    }

    @Test
    @DisplayName("测试支付回调成功场景")
    void testPaymentNotifySuccess() throws Exception {
        // 准备测试数据
        doNothing().when(paymentNotifyService).saveNotifyRecord(
                eq(TEST_CHANNEL), eq(TEST_SN), eq(TEST_PRICE), eq(TEST_TIMESTAMP), eq(TEST_SKU)
        );

        // 执行请求
        MvcResult result = mockMvc.perform(post("/notify/{channel}", TEST_CHANNEL)
                .param("sn", TEST_SN)
                .param("price", TEST_PRICE.toString())
                .param("timestamp", TEST_TIMESTAMP.toString())
                .param("sku", TEST_SKU)
                .param("nonce", TEST_NONCE)
                .param("sign", TEST_SIGN))
                .andExpect(status().isOk())
                .andReturn();

        // 验证响应
        String responseContent = result.getResponse().getContentAsString();
        AjaxResult<?> response = objectMapper.readValue(responseContent, AjaxResult.class);
        
        assertEquals(0, response.getCode());
        
        // 验证服务方法被调用
        verify(paymentNotifyService, times(1)).saveNotifyRecord(
                TEST_CHANNEL, TEST_SN, TEST_PRICE, TEST_TIMESTAMP, TEST_SKU
        );
    }

    @Test
    @DisplayName("测试参数验证失败场景")
    void testPaymentNotifyValidationFailure() throws Exception {
        // 测试空的设备序列号
        mockMvc.perform(post("/notify/{channel}", TEST_CHANNEL)
                .param("sn", "")  // 空字符串
                .param("price", TEST_PRICE.toString())
                .param("timestamp", TEST_TIMESTAMP.toString())
                .param("sku", TEST_SKU)
                .param("nonce", TEST_NONCE)
                .param("sign", TEST_SIGN))
                .andExpect(status().isBadRequest());

        // 测试负数金额
        mockMvc.perform(post("/notify/{channel}", TEST_CHANNEL)
                .param("sn", TEST_SN)
                .param("price", "-100")  // 负数
                .param("timestamp", TEST_TIMESTAMP.toString())
                .param("sku", TEST_SKU)
                .param("nonce", TEST_NONCE)
                .param("sign", TEST_SIGN))
                .andExpect(status().isBadRequest());

        // 验证服务方法未被调用
        verify(paymentNotifyService, never()).saveNotifyRecord(anyString(), anyString(), anyLong(), anyLong(), anyString());
    }

    @Test
    @DisplayName("测试生成测试参数接口")
    void testGenerateTestParams() throws Exception {
        MvcResult result = mockMvc.perform(get("/notify/test/generate-params"))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        AjaxResult<?> response = objectMapper.readValue(responseContent, AjaxResult.class);
        
        assertEquals(0, response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    @DisplayName("测试缺少必需参数的场景")
    void testMissingRequiredParameters() throws Exception {
        // 测试缺少sn参数
        mockMvc.perform(post("/notify/{channel}", TEST_CHANNEL)
                .param("price", TEST_PRICE.toString())
                .param("timestamp", TEST_TIMESTAMP.toString())
                .param("sku", TEST_SKU)
                .param("nonce", TEST_NONCE)
                .param("sign", TEST_SIGN))
                .andExpect(status().isBadRequest());

        // 测试缺少price参数
        mockMvc.perform(post("/notify/{channel}", TEST_CHANNEL)
                .param("sn", TEST_SN)
                .param("timestamp", TEST_TIMESTAMP.toString())
                .param("sku", TEST_SKU)
                .param("nonce", TEST_NONCE)
                .param("sign", TEST_SIGN))
                .andExpect(status().isBadRequest());
    }
}
